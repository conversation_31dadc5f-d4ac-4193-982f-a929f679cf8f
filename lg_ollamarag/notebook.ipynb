{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (0.3.18)\n", "Collecting langchain\n", "  Downloading langchain-0.3.19-py3-none-any.whl.metadata (7.9 kB)\n", "Requirement already satisfied: langchain_community in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (0.3.17)\n", "Collecting tik<PERSON>en\n", "  Downloading tiktoken-0.9.0-cp311-cp311-macosx_10_12_x86_64.whl.metadata (6.7 kB)\n", "Collecting langchain-nomic\n", "  Downloading langchain_nomic-0.1.4-py3-none-any.whl.metadata (1.6 kB)\n", "Requirement already satisfied: langchain-ollama in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (0.2.3)\n", "Requirement already satisfied: scikit-learn in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (1.6.1)\n", "Collecting langgraph\n", "  Downloading langgraph-0.2.73-py3-none-any.whl.metadata (17 kB)\n", "Collecting tavily-python\n", "  Downloading tavily_python-0.5.1-py3-none-any.whl.metadata (91 kB)\n", "Collecting bs4\n", "  Downloading bs4-0.0.2-py2.py3-none-any.whl.metadata (411 bytes)\n", "Collecting nomic[local]\n", "  Downloading nomic-3.4.1.tar.gz (49 kB)\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hCollecting langchain-core<1.0.0,>=0.3.35 (from langchain)\n", "  Downloading langchain_core-0.3.35-py3-none-any.whl.metadata (5.9 kB)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.6 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (0.3.6)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (0.3.6)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (2.10.6)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (2.0.37)\n", "Requirement already satisfied: requests<3,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (3.11.12)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (9.0.0)\n", "Requirement already satisfied: numpy<2,>=1.26.4 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain) (1.26.4)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain_community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain_community) (2.7.1)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain_community) (0.4.0)\n", "Requirement already satisfied: regex>=2022.1.18 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from tiktoken) (2023.12.25)\n", "Collecting pillow<11.0.0,>=10.3.0 (from langchain-nomic)\n", "  Downloading pillow-10.4.0-cp311-cp311-macosx_10_10_x86_64.whl.metadata (9.2 kB)\n", "Requirement already satisfied: click in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from nomic[local]) (8.1.8)\n", "Collecting jsonlines (from nomic[local])\n", "  Downloading jsonlines-4.0.0-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting loguru (from nomic[local])\n", "  Downloading loguru-0.7.3-py3-none-any.whl.metadata (22 kB)\n", "Requirement already satisfied: rich in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from nomic[local]) (13.9.4)\n", "Requirement already satisfied: pandas in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from nomic[local]) (2.2.3)\n", "Requirement already satisfied: tqdm in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from nomic[local]) (4.67.1)\n", "Collecting pyarrow (from nomic[local])\n", "  Downloading pyarrow-19.0.0-cp311-cp311-macosx_12_0_x86_64.whl.metadata (3.3 kB)\n", "Collecting pyjwt (from nomic[local])\n", "  Downloading PyJWT-2.10.1-py3-none-any.whl.metadata (4.0 kB)\n", "Collecting gpt4all<3,>=2.5.0 (from nomic[local])\n", "  Downloading gpt4all-2.8.2-py3-none-macosx_10_15_universal2.whl.metadata (4.8 kB)\n", "Requirement already satisfied: ollama<1,>=0.4.4 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-ollama) (0.4.7)\n", "Requirement already satisfied: scipy>=1.6.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from scikit-learn) (1.15.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from scikit-learn) (1.4.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from scikit-learn) (3.5.0)\n", "Collecting langgraph-checkpoint<3.0.0,>=2.0.10 (from langgraph)\n", "  Downloading langgraph_checkpoint-2.0.15-py3-none-any.whl.metadata (4.6 kB)\n", "Collecting langgraph-sdk<0.2.0,>=0.1.42 (from langgraph)\n", "  Downloading langgraph_sdk-0.1.51-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: httpx in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from tavily-python) (0.28.1)\n", "Requirement already satisfied: beautifulsoup4 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from bs4) (4.13.3)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (2.4.4)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (25.1.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (6.1.0)\n", "Requirement already satisfied: propcache>=0.2.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (0.2.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain) (1.18.3)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (3.20.2)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (0.9.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain) (23.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain) (4.12.2)\n", "Collecting msgpack<2.0.0,>=1.1.0 (from langgraph-checkpoint<3.0.0,>=2.0.10->langgraph)\n", "  Downloading msgpack-1.1.0-cp311-cp311-macosx_10_9_x86_64.whl.metadata (8.4 kB)\n", "Requirement already satisfied: orjson>=3.10.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph) (3.10.15)\n", "Requirement already satisfied: anyio in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx->tavily-python) (4.8.0)\n", "Requirement already satisfied: certifi in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx->tavily-python) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx->tavily-python) (1.0.7)\n", "Requirement already satisfied: idna in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx->tavily-python) (3.10)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpcore==1.*->httpx->tavily-python) (0.14.0)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.17->langchain) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.17->langchain) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.27.2)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain_community) (1.0.1)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langchain) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langchain) (1.26.18)\n", "Requirement already satisfied: greenlet!=0.4.17 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from SQLAlchemy<3,>=1.4->langchain) (3.1.1)\n", "Requirement already satisfied: soupsieve>1.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from beautifulsoup4->bs4) (2.6)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pandas->nomic[local]) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pandas->nomic[local]) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pandas->nomic[local]) (2025.1)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from rich->nomic[local]) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from rich->nomic[local]) (2.19.1)\n", "Requirement already satisfied: jsonpointer>=1.9 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.35->langchain) (3.0.0)\n", "Requirement already satisfied: mdurl~=0.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from markdown-it-py>=2.2.0->rich->nomic[local]) (0.1.2)\n", "Requirement already satisfied: six>=1.5 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas->nomic[local]) (1.16.0)\n", "Requirement already satisfied: mypy_extensions>=0.3.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community) (1.0.0)\n", "Requirement already satisfied: sniffio>=1.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from anyio->httpx->tavily-python) (1.3.1)\n", "Downloading langchain-0.3.19-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m15.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tiktoken-0.9.0-cp311-cp311-macosx_10_12_x86_64.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m13.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading langchain_nomic-0.1.4-py3-none-any.whl (3.9 kB)\n", "Downloading langgraph-0.2.73-py3-none-any.whl (151 kB)\n", "Downloading tavily_python-0.5.1-py3-none-any.whl (43 kB)\n", "Downloading bs4-0.0.2-py2.py3-none-any.whl (1.2 kB)\n", "Downloading gpt4all-2.8.2-py3-none-macosx_10_15_universal2.whl (6.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m6.6/6.6 MB\u001b[0m \u001b[31m12.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading langchain_core-0.3.35-py3-none-any.whl (413 kB)\n", "Downloading langgraph_checkpoint-2.0.15-py3-none-any.whl (38 kB)\n", "Downloading langgraph_sdk-0.1.51-py3-none-any.whl (44 kB)\n", "Downloading pillow-10.4.0-cp311-cp311-macosx_10_10_x86_64.whl (3.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m3.5/3.5 MB\u001b[0m \u001b[31m913.0 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading jsonlines-4.0.0-py3-none-any.whl (8.7 kB)\n", "Downloading loguru-0.7.3-py3-none-any.whl (61 kB)\n", "Downloading pyarrow-19.0.0-cp311-cp311-macosx_12_0_x86_64.whl (32.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m32.1/32.1 MB\u001b[0m \u001b[31m26.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hDownloading PyJWT-2.10.1-py3-none-any.whl (22 kB)\n", "Downloading msgpack-1.1.0-cp311-cp311-macosx_10_9_x86_64.whl (84 kB)\n", "Building wheels for collected packages: nomic\n", "  Building wheel for nomic (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for nomic: filename=nomic-3.4.1-py3-none-any.whl size=49942 sha256=57056c468c69149eadaa135915b74f2246c9059f1a888384ed9af8a68a89a3d7\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/d3/c6/51/4a3cd698715ef6570c9311a5ec5bbf972d41d0c4f3d500e8e3\n", "Successfully built nomic\n", "Installing collected packages: pyjwt, pyarrow, pillow, msgpack, loguru, jsonlines, tiktoken, gpt4all, bs4, tavily-python, nomic, langgraph-sdk, langchain-core, langgraph-checkpoint, langchain-nomic, langgraph, langchain\n", "  Attempting uninstall: pillow\n", "    Found existing installation: pillow 11.1.0\n", "    Uninstalling pillow-11.1.0:\n", "      Successfully uninstalled pillow-11.1.0\n", "  Attempting uninstall: langchain-core\n", "    Found existing installation: langchain-core 0.3.34\n", "    Uninstalling langchain-core-0.3.34:\n", "      Successfully uninstalled langchain-core-0.3.34\n", "  Attempting uninstall: langchain\n", "    Found existing installation: langchain 0.3.18\n", "    Uninstalling langchain-0.3.18:\n", "      Successfully uninstalled langchain-0.3.18\n", "Successfully installed bs4-0.0.2 gpt4all-2.8.2 jsonlines-4.0.0 langchain-0.3.19 langchain-core-0.3.35 langchain-nomic-0.1.4 langgraph-0.2.73 langgraph-checkpoint-2.0.15 langgraph-sdk-0.1.51 loguru-0.7.3 msgpack-1.1.0 nomic-3.4.1 pillow-10.4.0 pyarrow-19.0.0 pyjwt-2.10.1 tavily-python-0.5.1 tiktoken-0.9.0\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install -U langchain langchain_community tiktoken langchain-nomic \"nomic[local]\" langchain-ollama scikit-learn langgraph tavily-python bs4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON> pull"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["UsageError: Line magic function `%ollama` not found.\n"]}], "source": ["%ollama pull llama3.2:3b-instruct-fp16"]}], "metadata": {"kernelspec": {"display_name": "rag", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 2}