import PyPDF2
import json
import pandas as pd
from docx import Document
import os
import faiss
import numpy as np
import chromadb
from langchain_ollama import OllamaEmbeddings, OllamaLLM
from tqdm import tqdm

""" chroma_client = chromadb.PersistentClient(path=os.path.join(os.getcwd(), "chroma_db"))
llm_model = "llama3.2" """

def extract_text_from_pdf(file_path):
    with open(file_path, 'rb') as file:
        pdf = PyPDF2.PdfReader(file)
        text = ''
        for page in pdf.pages:
            text += page.extract_text()
        return text

def extract_text_from_docx(file_path):
    doc = Document(file_path)
    text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
    return text

def load_documents(folder_path):
    documents = []
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        if filename.endswith(".txt"):
            with open(file_path, "r", encoding="utf-8") as file:
                documents.append(file.read())
        elif filename.endswith(".pdf"):
            documents.append(extract_text_from_pdf(file_path))
        elif filename.endswith(".docx"):
            documents.append(extract_text_from_docx(file_path))
        elif filename.endswith(".csv"):
            df = pd.read_csv(file_path)
            documents.extend(df["content"].tolist())
        elif filename.endswith(".json"):
            with open(file_path, "r", encoding="utf-8") as file:
                data = json.load(file)
                documents.extend([item["content"] for item in data])
    return documents

def clean_text(text):
    text = text.strip()
    text = text.replace("\n", " ")
    return text

""" def add_to_index(index, model, documents, threshold=0.99):
    new_vectors = model.encode(documents)
    unique_vectors = []
    unique_documents = []
    
    for doc, vec in zip(documents, new_vectors):
        D, I = index.search(np.array([vec]), 1)  # 1 legközelebbi találat keresése
        if D[0][0] > threshold:  # Ha nincs nagyon hasonló dokumentum az indexben
            unique_vectors.append(vec)
            unique_documents.append(doc)

    if unique_vectors:
        index.add(np.array(unique_vectors))
    
    return unique_documents """

def chunk_text_with_overlap(text, chunk_size, overlap):
    chunks = []
    step = chunk_size - overlap
    for i in range(0, len(text), step):
        chunks.append(text[i:i + chunk_size])

        if i + chunk_size >= len(text):
            break
    return chunks

class ChromaDBEmbeddingFunction:
    """
    Custom embedding function for ChromaDB using embeddings from Ollama.
    """
    def __init__(self, langchain_embeddings):
        self.langchain_embeddings = langchain_embeddings

    def __call__(self, input):
        # Ensure the input is in a list format for processing
        if isinstance(input, str):
            input = [input]
        return self.langchain_embeddings.embed_documents(input)
    
def add_documents_to_collection(collection, documents, ids):
    """
    Add documents to the ChromaDB collection if they don't already exist.
    Shows progress bar during the process.
    
    Args:
        collection: ChromaDB collection
        documents (list of str): The documents to add
        ids (list of str): Unique IDs for the documents
    """
    print("Meglévő dokumentumok ellenőrzése...")
    existing_ids = set(collection.get(ids=list(ids))['ids'])
    
    # Filter out existing documents with progress bar
    new_documents = []
    new_ids = []
    print("Új dokumentumok szűrése...")
    for doc, doc_id in tqdm(zip(documents, ids), total=len(ids), desc="Dokumentumok szűrése"):
        if doc_id not in existing_ids:
            new_documents.append(doc)
            new_ids.append(doc_id)

    if new_documents:
        total_new = len(new_documents)
        print(f"\nÖsszesen {total_new} új dokumentum hozzáadása...")
        
        # Process in batches with progress bar
        batch_size = 50
        for i in tqdm(range(0, len(new_documents), batch_size), desc="Dokumentumok hozzáadása"):
            batch_docs = new_documents[i:i + batch_size]
            batch_ids = new_ids[i:i + batch_size]
            
            collection.add(
                documents=batch_docs,
                ids=batch_ids
            )
        
        print(f"\nSikeresen hozzáadva {total_new} új dokumentum")
    else:
        print("\nNincs új dokumentum hozzáadva")

def query_chromadb(collection, query_text, n_results=1):
    """
    Query the ChromaDB collection for relevant documents.
    
    Args:
        query_text (str): The input query.
        n_results (int): The number of top results to return.
    
    Returns:
        list of dict: The top matching documents and their metadata.
    """
    results = collection.query(
        query_texts=[query_text],
        n_results=n_results
    )
    return results["documents"], results["metadatas"]

# Function to interact with the Ollama LLM
def query_ollama(prompt, llm_model="llama3.2"):
    """
    Send a query to Ollama and retrieve the response.
    
    Args:
        prompt (str): The input prompt for Ollama.
    
    Returns:
        str: The response from Ollama.
    """
    llm = OllamaLLM(model=llm_model)
    return llm.invoke(prompt)

# RAG pipeline: Combine ChromaDB and Ollama for Retrieval-Augmented Generation
def rag_pipeline(query_text, collection, llm_model="llama3.2"):
    """
    Perform Retrieval-Augmented Generation (RAG) by combining ChromaDB and Ollama.
    
    Args:
        query_text (str): The input query.
    
    Returns:
        str: The generated response from Ollama augmented with retrieved context.
    """
    # Step 1: Retrieve relevant documents from ChromaDB
    retrieved_docs, metadata = query_chromadb(collection, query_text, 3)
    context = " ".join(retrieved_docs[0]) if retrieved_docs else "No relevant documents found."

    # Step 2: Send the query along with the context to Ollama
    augmented_prompt = f"Context: {context}\n\nQuestion: {query_text}\nAnswer:"
    print("######## Augmented Prompt ########")
    print(augmented_prompt)

    response = query_ollama(augmented_prompt, llm_model)
    return response