import mylib as mylib
import os
from langchain_ollama import OllamaEmbeddings, OllamaLLM
import chromadb
import hashlib

folder_path = "dok2text/dok"
print (f"Dokumentumok betöltése a következő mappából: {folder_path}")
documents = mylib.load_documents(folder_path)
print(f"Betöltött dokumentumok száma: {len(documents)}")

documents = [mylib.clean_text(doc) for doc in documents]

#print (documents)

chunks = []
for doc in documents:
    chunks.extend(mylib.chunk_text_with_overlap(doc, chunk_size=500, overlap=100))

print (f"Darabok száma: {len(chunks)}")

llm_model = "llama3.2"
embedding = mylib.ChromaDBEmbeddingFunction(
    OllamaEmbeddings(
        model=llm_model,
        base_url="http://localhost:11434"  # Adjust the base URL as per your Ollama server configuration
    )
)

chroma_client = chromadb.PersistentClient(path=os.path.join(os.getcwd(), "chroma_db"))
collection_name = "rag_collection_demo_1"
collection = chroma_client.get_or_create_collection(
    name=collection_name,
    metadata={"description": "A collection for RAG with Ollama - Demo1"},
    embedding_function=embedding  # Use the custom embedding function
)
chunk_ids = []
for chunk in chunks:
    chunk_ids.append(hashlib.md5(chunk.encode()).hexdigest())

""" chunks = [
    "Artificial intelligence is the simulation of human intelligence processes by machines.",
    "Python is a programming language that lets you work quickly and integrate systems more effectively.",
    "ChromaDB is a vector database designed for AI applications."
]
chunk_ids = ["doc1", "doc2", "doc3"] """

print(f"A collection EREDETI mérete: {collection.count()}")

mylib.add_documents_to_collection(collection, chunks, chunk_ids)

print(f"A collection ÚJ mérete: {collection.count()}")