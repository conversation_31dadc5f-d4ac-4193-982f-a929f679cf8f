{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<!doctype html>\n", "<html lang=en>\n", "  <head>\n", "    <title>RuntimeError: Could not get the OCRAgent instance. Please check the OCR package and the OCR_AGENT environment variable.\n", " // Werkzeug Debugger</title>\n", "    <link rel=\"stylesheet\" href=\"?__debugger__=yes&amp;cmd=resource&amp;f=style.css\">\n", "    <link rel=\"shortcut icon\"\n", "        href=\"?__debugger__=yes&amp;cmd=resource&amp;f=console.png\">\n", "    <script src=\"?__debugger__=yes&amp;cmd=resource&amp;f=debugger.js\"></script>\n", "    <script>\n", "      var CONSOLE_MODE = false,\n", "          EVALEX = true,\n", "          EVALEX_TRUSTED = false,\n", "          SECRET = \"K5QTOcoN3g6Id95QSPRf\";\n", "    </script>\n", "  </head>\n", "  <body style=\"background-color: #fff\">\n", "    <div class=\"debugger\">\n", "<h1>RuntimeError</h1>\n", "<div class=\"detail\">\n", "  <p class=\"errormsg\">RuntimeError: Could not get the OCRAgent instance. Please check the OCR package and the OCR_AGENT environment variable.\n", "</p>\n", "</div>\n", "<h2 class=\"traceback\">Traceback <em>(most recent call last)</em></h2>\n", "<div class=\"traceback\">\n", "  <h3></h3>\n", "  <ul><li><div class=\"frame\" id=\"frame-6082910464\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py\"</cite>,\n", "      line <em class=\"line\">47</em>,\n", "      in <code class=\"function\">get_instance</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                </span>f&#34;Environment variable OCR_AGENT module name {module_name} must be set to a &#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>f&#34;whitelisted module part of {OCR_AGENT_MODULES_WHITELIST}.&#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>try:</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>module = importlib.import_module(module_name)\n", "<span class=\"ws\">            </span>         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>loaded_class = getattr(module, class_name)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>return loaded_class(language)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>except (ImportError, AttributeError) as e:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>logger.error(f&#34;Failed to get OCRAgent instance: {e}&#34;)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>raise RuntimeError(</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082910608\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/importlib/__init__.py\"</cite>,\n", "      line <em class=\"line\">126</em>,\n", "      in <code class=\"function\">import_module</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">            </span>raise TypeError(msg.format(name))</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>for character in name:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>if character != &#39;.&#39;:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>break</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>level += 1</pre>\n", "<pre class=\"line current\"><span class=\"ws\">    </span>return _bootstrap._gcd_import(name[level:], package, level)\n", "<span class=\"ws\">    </span>       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span>_RELOADING = {}</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082910896\">\n", "  <h4>File <cite class=\"filename\">\"&lt;frozen importlib._bootstrap&gt;\"</cite>,\n", "      line <em class=\"line\">1204</em>,\n", "      in <code class=\"function\">_gcd_import</code></h4>\n", "  <div class=\"source \"></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082911040\">\n", "  <h4>File <cite class=\"filename\">\"&lt;frozen importlib._bootstrap&gt;\"</cite>,\n", "      line <em class=\"line\">1176</em>,\n", "      in <code class=\"function\">_find_and_load</code></h4>\n", "  <div class=\"source \"></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082911184\">\n", "  <h4>File <cite class=\"filename\">\"&lt;frozen importlib._bootstrap&gt;\"</cite>,\n", "      line <em class=\"line\">1147</em>,\n", "      in <code class=\"function\">_find_and_load_unlocked</code></h4>\n", "  <div class=\"source \"></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082911328\">\n", "  <h4>File <cite class=\"filename\">\"&lt;frozen importlib._bootstrap&gt;\"</cite>,\n", "      line <em class=\"line\">690</em>,\n", "      in <code class=\"function\">_load_unlocked</code></h4>\n", "  <div class=\"source \"></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082911472\">\n", "  <h4>File <cite class=\"filename\">\"&lt;frozen importlib._bootstrap_external&gt;\"</cite>,\n", "      line <em class=\"line\">940</em>,\n", "      in <code class=\"function\">exec_module</code></h4>\n", "  <div class=\"source \"></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082911616\">\n", "  <h4>File <cite class=\"filename\">\"&lt;frozen importlib._bootstrap&gt;\"</cite>,\n", "      line <em class=\"line\">241</em>,\n", "      in <code class=\"function\">_call_with_frames_removed</code></h4>\n", "  <div class=\"source \"></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082911760\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/tesseract_ocr.py\"</cite>,\n", "      line <em class=\"line\">10</em>,\n", "      in <code class=\"function\">&lt;module&gt;</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\"></span>from typing import TYPE_CHECKING</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span>import cv2</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span>import numpy as np</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span>import pandas as pd</pre>\n", "<pre class=\"line current\"><span class=\"ws\"></span>import unstructured_pytesseract</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span>from bs4 import BeautifulSoup, Tag</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span>from PIL import Image as PILImage</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span>from unstructured.logger import trace_logger</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span>from unstructured.partition.utils.config import env_config</pre></div>\n", "</div>\n", "\n", "<li><div class=\"exc-divider\">During handling of the above exception, another exception occurred:</div>\n", "<li><div class=\"frame\" id=\"frame-6008217104\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\"</cite>,\n", "      line <em class=\"line\">1536</em>,\n", "      in <code class=\"function\">__call__</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">    </span>) -&gt; cabc.Iterable[bytes]:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>&#34;&#34;&#34;The WSGI server calls the Flask application object as the</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>WSGI application. This calls :meth:`wsgi_app`, which can be</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>wrapped to apply middleware.</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>&#34;&#34;&#34;</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>return self.wsgi_app(environ, start_response)\n", "<span class=\"ws\">        </span>       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008217248\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\"</cite>,\n", "      line <em class=\"line\">1514</em>,\n", "      in <code class=\"function\">wsgi_app</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">            </span>try:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>ctx.push()</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>response = self.full_dispatch_request()</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>except Exception as e:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>error = e</pre>\n", "<pre class=\"line current\"><span class=\"ws\">                </span>response = self.handle_exception(e)\n", "<span class=\"ws\">                </span>           ^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>except:  # noqa: B001</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>error = sys.exc_info()[1]</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>raise</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>return response(environ, start_response)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>finally:</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008222864\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\"</cite>,\n", "      line <em class=\"line\">1511</em>,\n", "      in <code class=\"function\">wsgi_app</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">        </span>ctx = self.request_context(environ)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>error: BaseException | None = None</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>try:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>try:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>ctx.push()</pre>\n", "<pre class=\"line current\"><span class=\"ws\">                </span>response = self.full_dispatch_request()\n", "<span class=\"ws\">                </span>           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>except Exception as e:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>error = e</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>response = self.handle_exception(e)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>except:  # noqa: B001</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>error = sys.exc_info()[1]</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008222720\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\"</cite>,\n", "      line <em class=\"line\">919</em>,\n", "      in <code class=\"function\">full_dispatch_request</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">            </span>request_started.send(self, _async_wrapper=self.ensure_sync)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>rv = self.preprocess_request()</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>if rv is None:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>rv = self.dispatch_request()</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>except Exception as e:</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>rv = self.handle_user_exception(e)\n", "<span class=\"ws\">            </span>     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>return self.finalize_request(rv)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>def finalize_request(</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>self,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>rv: ft.ResponseReturnValue | HTTPException,</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008223296\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\"</cite>,\n", "      line <em class=\"line\">917</em>,\n", "      in <code class=\"function\">full_dispatch_request</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>try:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>request_started.send(self, _async_wrapper=self.ensure_sync)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>rv = self.preprocess_request()</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>if rv is None:</pre>\n", "<pre class=\"line current\"><span class=\"ws\">                </span>rv = self.dispatch_request()\n", "<span class=\"ws\">                </span>     ^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>except Exception as e:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>rv = self.handle_user_exception(e)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>return self.finalize_request(rv)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>def finalize_request(</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008223440\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\"</cite>,\n", "      line <em class=\"line\">902</em>,\n", "      in <code class=\"function\">dispatch_request</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">            </span>and req.method == &#34;OPTIONS&#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>return self.make_default_options_response()</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span># otherwise dispatch to the handler for that endpoint</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>view_args: dict[str, t.Any] = req.view_args  # type: ignore[assignment]</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n", "<span class=\"ws\">        </span>       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>def full_dispatch_request(self) -&gt; Response:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>&#34;&#34;&#34;Dispatches the request and on top of that performs request</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>pre and postprocessing as well as HTTP exception catching and</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>error handling.</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008223584\">\n", "  <h4>File <cite class=\"filename\">\"/Users/<USER>/Desktop/pythonenv/ragteszt/app.py\"</cite>,\n", "      line <em class=\"line\">37</em>,\n", "      in <code class=\"function\">route_embed</code></h4>\n", "  <div class=\"source \"><pre class=\"line before\"><span class=\"ws\">    </span>file = request.files[&#39;file&#39;]</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>if file.filename == &#39;&#39;:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>return jsonify({&#34;error&#34;: &#34;No selected file&#34;}), 400</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line current\"><span class=\"ws\">    </span>embedded = embed(file)\n", "<span class=\"ws\">    </span>           ^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>if embedded:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>return jsonify({&#34;message&#34;: &#34;File embedded successfully&#34;}), 200</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>return jsonify({&#34;error&#34;: &#34;File embedded unsuccessfully&#34;}), 400</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008223728\">\n", "  <h4>File <cite class=\"filename\">\"/Users/<USER>/Desktop/pythonenv/ragteszt/embed.py\"</cite>,\n", "      line <em class=\"line\">45</em>,\n", "      in <code class=\"function\">embed</code></h4>\n", "  <div class=\"source \"><pre class=\"line before\"><span class=\"ws\"></span># Main function to handle the embedding process</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span>def embed(file):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span># Check if the file is valid, save it, load and split the data, add to the database, and remove the temporary file</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>if file.filename != &#39;&#39; and file and allowed_file(file.filename):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>file_path = save_file(file)</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>chunks = load_and_split_data(file_path)\n", "<span class=\"ws\">        </span>         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>db = get_vector_db()</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>db.add_documents(chunks)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>db.persist()</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>os.remove(file_path)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008223872\">\n", "  <h4>File <cite class=\"filename\">\"/Users/<USER>/Desktop/pythonenv/ragteszt/embed.py\"</cite>,\n", "      line <em class=\"line\">34</em>,\n", "      in <code class=\"function\">load_and_split_data</code></h4>\n", "  <div class=\"source \"><pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span># Function to load and split the data from the PDF file</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span>def load_and_split_data(file_path):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span># Load the PDF file and split the data into chunks</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>loader = UnstructuredPDFLoader(file_path=file_path)</pre>\n", "<pre class=\"line current\"><span class=\"ws\">    </span>data = loader.load()\n", "<span class=\"ws\">    </span>       ^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>text_splitter = RecursiveCharacterTextSplitter(chunk_size=7500, chunk_overlap=100)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>chunks = text_splitter.split_documents(data)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>return chunks</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008224016\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_core/document_loaders/base.py\"</cite>,\n", "      line <em class=\"line\">31</em>,\n", "      in <code class=\"function\">load</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span># Sub-classes should not implement this method directly. Instead, they</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span># should implement the lazy load method.</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>def load(self) -&gt; list[Document]:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>&#34;&#34;&#34;Load data into Document objects.&#34;&#34;&#34;</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>return list(self.lazy_load())\n", "<span class=\"ws\">        </span>       ^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>async def aload(self) -&gt; list[Document]:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>&#34;&#34;&#34;Load data into Document objects.&#34;&#34;&#34;</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>return [document async for document in self.alazy_load()]</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008224160\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_community/document_loaders/unstructured.py\"</cite>,\n", "      line <em class=\"line\">107</em>,\n", "      in <code class=\"function\">lazy_load</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                </span>element.apply(post_processor)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>return elements</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>def lazy_load(self) -&gt; Iterator[Document]:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>&#34;&#34;&#34;Load file.&#34;&#34;&#34;</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>elements = self._get_elements()\n", "<span class=\"ws\">        </span>           ^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>self._post_process_elements(elements)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>if self.mode == &#34;elements&#34;:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>for element in elements:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>metadata = self._get_metadata()</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span># NOTE(MthwRobinson) - the attribute check is for backward compatibility</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008224304\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_community/document_loaders/pdf.py\"</cite>,\n", "      line <em class=\"line\">94</em>,\n", "      in <code class=\"function\">_get_elements</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">        </span>super().__init__(file_path=file_path, mode=mode, **unstructured_kwargs)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>def _get_elements(self) -&gt; list:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>from unstructured.partition.pdf import partition_pdf</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>return partition_pdf(filename=self.file_path, **self.unstructured_kwargs)  # type: ignore[arg-type]\n", "<span class=\"ws\">        </span>       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span>class BasePDFLoader(BaseLoader, ABC):</pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>&#34;&#34;&#34;Base Loader class for `PDF` files.</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6008224448\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/documents/elements.py\"</cite>,\n", "      line <em class=\"line\">581</em>,\n", "      in <code class=\"function\">wrapper</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                    </span>+ &#34;\\n\\t\\tThe filename to use in element metadata.&#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>@functools.wraps(func)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>def wrapper(*args: _P.args, **kwargs: _P.kwargs) -&gt; list[Element]:</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>elements = func(*args, **kwargs)\n", "<span class=\"ws\">            </span>           ^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>call_args = get_call_args_applying_defaults(func, *args, **kwargs)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>unique_element_ids: bool = call_args.get(&#34;unique_element_ids&#34;, False)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>if unique_element_ids is False:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>elements = assign_and_map_hash_ids(elements)</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082683824\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/file_utils/filetype.py\"</cite>,\n", "      line <em class=\"line\">788</em>,\n", "      in <code class=\"function\">wrapper</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">    </span>&#34;&#34;&#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>def decorator(func: Callable[_P, list[Element]]) -&gt; Callable[_P, list[Element]]:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>@functools.wraps(func)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>def wrapper(*args: _P.args, **kwargs: _P.kwargs) -&gt; list[Element]:</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>elements = func(*args, **kwargs)\n", "<span class=\"ws\">            </span>           ^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>for element in elements:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span># NOTE(robinson) - Attached files have already run through this logic</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span># in their own partitioning function</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>if element.metadata.attached_to_filename is None:</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082792320\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/file_utils/filetype.py\"</cite>,\n", "      line <em class=\"line\">746</em>,\n", "      in <code class=\"function\">wrapper</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span>def add_metadata(func: Callable[_P, list[Element]]) -&gt; Callable[_P, list[Element]]:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>@functools.wraps(func)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>def wrapper(*args: _P.args, **kwargs: _P.kwargs) -&gt; list[Element]:</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>elements = func(*args, **kwargs)\n", "<span class=\"ws\">        </span>           ^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>call_args = get_call_args_applying_defaults(func, *args, **kwargs)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>if call_args.get(&#34;metadata_filename&#34;):</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>call_args[&#34;filename&#34;] = call_args.get(&#34;metadata_filename&#34;)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082792464\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/chunking/dispatch.py\"</cite>,\n", "      line <em class=\"line\">74</em>,\n", "      in <code class=\"function\">wrapper</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">    </span>@functools.wraps(func)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>def wrapper(*args: _P.args, **kwargs: _P.kwargs) -&gt; list[Element]:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>&#34;&#34;&#34;The decorated function is replaced with this one.&#34;&#34;&#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span># -- call the partitioning function to get the elements --</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>elements = func(*args, **kwargs)\n", "<span class=\"ws\">        </span>           ^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span># -- look for a chunking-strategy argument --</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>call_args = get_call_args_applying_defaults(func, *args, **kwargs)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>chunking_strategy = call_args.pop(&#34;chunking_strategy&#34;, None)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082794768\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py\"</cite>,\n", "      line <em class=\"line\">211</em>,\n", "      in <code class=\"function\">partition_pdf</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>exactly_one(filename=filename, file=file)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>languages = check_language_args(languages or [], ocr_languages)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line current\"><span class=\"ws\">    </span>return partition_pdf_or_image(\n", "<span class=\"ws\">    </span>       </pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>filename=filename,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>file=file,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>include_page_breaks=include_page_breaks,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>strategy=strategy,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>infer_table_structure=infer_table_structure,</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082794912\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py\"</cite>,\n", "      line <em class=\"line\">307</em>,\n", "      in <code class=\"function\">partition_pdf_or_image</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>if strategy == PartitionStrategy.HI_RES:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span># NOTE(robinson): Catches a UserWarning that occurs when detection is called</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>with warnings.catch_warnings():</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>warnings.simplefilter(&#34;ignore&#34;)</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>elements = _partition_pdf_or_image_local(\n", "<span class=\"ws\">            </span>           </pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>filename=filename,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>file=spooled_to_bytes_io_if_needed(file),</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>is_image=is_image,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>infer_table_structure=infer_table_structure,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>include_page_breaks=include_page_breaks,</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082795056\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py\"</cite>,\n", "      line <em class=\"line\">216</em>,\n", "      in <code class=\"function\">wrapper</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>@wraps(func)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>def wrapper(*args: _P.args, **kwargs: _P.kwargs):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>run_check()</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>return func(*args, **kwargs)\n", "<span class=\"ws\">            </span>       ^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>@wraps(func)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>async def wrapper_async(*args: _P.args, **kwargs: _P.kwargs):</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>run_check()</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>return await func(*args, **kwargs)</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082804848\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py\"</cite>,\n", "      line <em class=\"line\">628</em>,\n", "      in <code class=\"function\">_partition_pdf_or_image_local</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">            </span>inferred_document_layout=inferred_document_layout,</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>extracted_layout=extracted_layout,</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>hi_res_model_name=hi_res_model_name,</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>final_document_layout = process_file_with_ocr(\n", "<span class=\"ws\">        </span>                        </pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>filename,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>merged_document_layout,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>extracted_layout=extracted_layout,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>is_image=is_image,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>infer_table_structure=infer_table_structure,</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082804992\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py\"</cite>,\n", "      line <em class=\"line\">216</em>,\n", "      in <code class=\"function\">wrapper</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>@wraps(func)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>def wrapper(*args: _P.args, **kwargs: _P.kwargs):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>run_check()</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>return func(*args, **kwargs)\n", "<span class=\"ws\">            </span>       ^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>@wraps(func)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>async def wrapper_async(*args: _P.args, **kwargs: _P.kwargs):</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>run_check()</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>return await func(*args, **kwargs)</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082805136\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py\"</cite>,\n", "      line <em class=\"line\">186</em>,\n", "      in <code class=\"function\">process_file_with_ocr</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                        </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                        </span>merged_page_layouts.append(merged_page_layout)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>return DocumentLayout.from_pages(merged_page_layouts)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>except Exception as e:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>if os.path.isdir(filename) or os.path.isfile(filename):</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>raise e\n", "<span class=\"ws\">            </span>^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>else:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>raise FileNotFoundError(f&#39;File &#34;{filename}&#34; not found!&#39;) from e</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span>@requires_dependencies(&#34;unstructured_inference&#34;)</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082908592\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py\"</cite>,\n", "      line <em class=\"line\">173</em>,\n", "      in <code class=\"function\">process_file_with_ocr</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>image_paths = cast(List[str], _image_paths)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                </span>for i, image_path in enumerate(image_paths):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                    </span>extracted_regions = extracted_layout[i] if i &lt; len(extracted_layout) else None</pre>\n", "<pre class=\"line before\"><span class=\"ws\">                    </span>with PILImage.open(image_path) as image:</pre>\n", "<pre class=\"line current\"><span class=\"ws\">                        </span>merged_page_layout = supplement_page_layout_with_ocr(\n", "<span class=\"ws\">                        </span>                     </pre>\n", "<pre class=\"line after\"><span class=\"ws\">                            </span>page_layout=out_layout.pages[i],</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                            </span>image=image,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                            </span>infer_table_structure=infer_table_structure,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                            </span>ocr_languages=ocr_languages,</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                            </span>ocr_mode=ocr_mode,</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082908736\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py\"</cite>,\n", "      line <em class=\"line\">216</em>,\n", "      in <code class=\"function\">wrapper</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">                </span>)</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>@wraps(func)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>def wrapper(*args: _P.args, **kwargs: _P.kwargs):</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>run_check()</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>return func(*args, **kwargs)\n", "<span class=\"ws\">            </span>       ^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>@wraps(func)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>async def wrapper_async(*args: _P.args, **kwargs: _P.kwargs):</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>run_check()</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>return await func(*args, **kwargs)</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082908880\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py\"</cite>,\n", "      line <em class=\"line\">209</em>,\n", "      in <code class=\"function\">supplement_page_layout_with_ocr</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">    </span>merge it with PageLayout.</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>If mode is &#34;individual_blocks&#34;, we find the elements from PageLayout</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>with no text and add text from OCR to each element.</pre>\n", "<pre class=\"line before\"><span class=\"ws\">    </span>&#34;&#34;&#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line current\"><span class=\"ws\">    </span>ocr_agent = OCRAgent.get_agent(language=ocr_languages)\n", "<span class=\"ws\">    </span>            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>if ocr_mode == OCRMode.FULL_PAGE.value:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>ocr_layout = ocr_agent.get_layout_from_image(image)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>if ocr_layout_dumper:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>ocr_layout_dumper.add_ocred_page(ocr_layout.as_list())</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>page_layout.elements_array = merge_out_layout_with_ocr_layout(</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082909024\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py\"</cite>,\n", "      line <em class=\"line\">34</em>,\n", "      in <code class=\"function\">get_agent</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">        </span>&#34;&#34;&#34;Get the configured OCRAgent instance.</pre>\n", "<pre class=\"line before\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>The OCR package used by the agent is determined by the `OCR_AGENT` environment variable.</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>&#34;&#34;&#34;</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>ocr_agent_cls_qname = cls._get_ocr_agent_cls_qname()</pre>\n", "<pre class=\"line current\"><span class=\"ws\">        </span>return cls.get_instance(ocr_agent_cls_qname, language)\n", "<span class=\"ws\">        </span>       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>@staticmethod</pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>@functools.lru_cache(maxsize=None)</pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>def get_instance(ocr_agent_module: str, language: str) -&gt; &#34;OCRAgent&#34;:</pre>\n", "<pre class=\"line after\"><span class=\"ws\">        </span>module_name, class_name = ocr_agent_module.rsplit(&#34;.&#34;, 1)</pre></div>\n", "</div>\n", "\n", "<li><div class=\"frame\" id=\"frame-6082910320\">\n", "  <h4>File <cite class=\"filename\">\"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py\"</cite>,\n", "      line <em class=\"line\">52</em>,\n", "      in <code class=\"function\">get_instance</code></h4>\n", "  <div class=\"source library\"><pre class=\"line before\"><span class=\"ws\">            </span>module = importlib.import_module(module_name)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>loaded_class = getattr(module, class_name)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>return loaded_class(language)</pre>\n", "<pre class=\"line before\"><span class=\"ws\">        </span>except (ImportError, AttributeError) as e:</pre>\n", "<pre class=\"line before\"><span class=\"ws\">            </span>logger.error(f&#34;Failed to get OCRAgent instance: {e}&#34;)</pre>\n", "<pre class=\"line current\"><span class=\"ws\">            </span>raise RuntimeError(\n", "<span class=\"ws\">            </span>^</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>&#34;Could not get the OCRAgent instance. Please check the OCR package and the &#34;</pre>\n", "<pre class=\"line after\"><span class=\"ws\">                </span>&#34;OCR_AGENT environment variable.&#34;</pre>\n", "<pre class=\"line after\"><span class=\"ws\">            </span>)</pre>\n", "<pre class=\"line after\"><span class=\"ws\"></span> </pre>\n", "<pre class=\"line after\"><span class=\"ws\">    </span>@abstractmethod</pre></div>\n", "</div>\n", "</ul>\n", "  <blockquote>RuntimeError: Could not get the OCRAgent instance. Please check the OCR package and the OCR_AGENT environment variable.\n", "</blockquote>\n", "</div>\n", "\n", "<div class=\"plain\">\n", "    <p>\n", "      This is the Copy/Paste friendly version of the traceback.\n", "    </p>\n", "    <textarea cols=\"50\" rows=\"10\" name=\"code\" readonly>Traceback (most recent call last):\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py&#34;, line 47, in get_instance\n", "    module = importlib.import_module(module_name)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/importlib/__init__.py&#34;, line 126, in import_module\n", "    return _bootstrap._gcd_import(name[level:], package, level)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;&lt;frozen importlib._bootstrap&gt;&#34;, line 1204, in _gcd_import\n", "  File &#34;&lt;frozen importlib._bootstrap&gt;&#34;, line 1176, in _find_and_load\n", "  File &#34;&lt;frozen importlib._bootstrap&gt;&#34;, line 1147, in _find_and_load_unlocked\n", "  File &#34;&lt;frozen importlib._bootstrap&gt;&#34;, line 690, in _load_unlocked\n", "  File &#34;&lt;frozen importlib._bootstrap_external&gt;&#34;, line 940, in exec_module\n", "  File &#34;&lt;frozen importlib._bootstrap&gt;&#34;, line 241, in _call_with_frames_removed\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/tesseract_ocr.py&#34;, line 10, in &lt;module&gt;\n", "    import unstructured_pytesseract\n", "ModuleNotFoundError: No module named &#39;unstructured_pytesseract&#39;\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py&#34;, line 1536, in __call__\n", "    return self.wsgi_app(environ, start_response)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py&#34;, line 1514, in wsgi_app\n", "    response = self.handle_exception(e)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py&#34;, line 1511, in wsgi_app\n", "    response = self.full_dispatch_request()\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py&#34;, line 919, in full_dispatch_request\n", "    rv = self.handle_user_exception(e)\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py&#34;, line 917, in full_dispatch_request\n", "    rv = self.dispatch_request()\n", "         ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py&#34;, line 902, in dispatch_request\n", "    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/Users/<USER>/Desktop/pythonenv/ragteszt/app.py&#34;, line 37, in route_embed\n", "    embedded = embed(file)\n", "               ^^^^^^^^^^^\n", "  File &#34;/Users/<USER>/Desktop/pythonenv/ragteszt/embed.py&#34;, line 45, in embed\n", "    chunks = load_and_split_data(file_path)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/Users/<USER>/Desktop/pythonenv/ragteszt/embed.py&#34;, line 34, in load_and_split_data\n", "    data = loader.load()\n", "           ^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_core/document_loaders/base.py&#34;, line 31, in load\n", "    return list(self.lazy_load())\n", "           ^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_community/document_loaders/unstructured.py&#34;, line 107, in lazy_load\n", "    elements = self._get_elements()\n", "               ^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_community/document_loaders/pdf.py&#34;, line 94, in _get_elements\n", "    return partition_pdf(filename=self.file_path, **self.unstructured_kwargs)  # type: ignore[arg-type]\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/documents/elements.py&#34;, line 581, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/file_utils/filetype.py&#34;, line 788, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/file_utils/filetype.py&#34;, line 746, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/chunking/dispatch.py&#34;, line 74, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py&#34;, line 211, in partition_pdf\n", "    return partition_pdf_or_image(\n", "           ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py&#34;, line 307, in partition_pdf_or_image\n", "    elements = _partition_pdf_or_image_local(\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py&#34;, line 216, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py&#34;, line 628, in _partition_pdf_or_image_local\n", "    final_document_layout = process_file_with_ocr(\n", "                            ^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py&#34;, line 216, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py&#34;, line 186, in process_file_with_ocr\n", "    raise e\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py&#34;, line 173, in process_file_with_ocr\n", "    merged_page_layout = supplement_page_layout_with_ocr(\n", "                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py&#34;, line 216, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py&#34;, line 209, in supplement_page_layout_with_ocr\n", "    ocr_agent = OCRAgent.get_agent(language=ocr_languages)\n", "                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py&#34;, line 34, in get_agent\n", "    return cls.get_instance(ocr_agent_cls_qname, language)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File &#34;/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py&#34;, line 52, in get_instance\n", "    raise RuntimeError(\n", "RuntimeError: Could not get the OCRAgent instance. Please check the OCR package and the OCR_AGENT environment variable.\n", "</textarea>\n", "</div>\n", "<div class=\"explanation\">\n", "  The debugger caught an exception in your WSGI application.  You can now\n", "  look at the traceback which led to the error.  <span class=\"nojavascript\">\n", "  If you enable JavaScript you can also use additional features such as code\n", "  execution (if the evalex feature is enabled), automatic pasting of the\n", "  exceptions and much more.</span>\n", "</div>\n", "      <div class=\"footer\">\n", "        Brought to you by <strong class=\"arthur\">DON'T PANIC</strong>, your\n", "        friendly Werkzeug powered traceback interpreter.\n", "      </div>\n", "    </div>\n", "\n", "    <div class=\"pin-prompt\">\n", "      <div class=\"inner\">\n", "        <h3><PERSON><PERSON><PERSON> Locked</h3>\n", "        <p>\n", "          The console is locked and needs to be unlocked by entering the PIN.\n", "          You can find the PIN printed out on the standard output of your\n", "          shell that runs the server.\n", "        <form>\n", "          <p>PIN:\n", "            <input type=text name=pin size=14>\n", "            <input type=submit name=btn value=\"Confirm Pin\">\n", "        </form>\n", "      </div>\n", "    </div>\n", "  </body>\n", "</html>\n", "\n", "<!--\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py\", line 47, in get_instance\n", "    module = importlib.import_module(module_name)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/importlib/__init__.py\", line 126, in import_module\n", "    return _bootstrap._gcd_import(name[level:], package, level)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"<frozen importlib._bootstrap>\", line 1204, in _gcd_import\n", "  File \"<frozen importlib._bootstrap>\", line 1176, in _find_and_load\n", "  File \"<frozen importlib._bootstrap>\", line 1147, in _find_and_load_unlocked\n", "  File \"<frozen importlib._bootstrap>\", line 690, in _load_unlocked\n", "  File \"<frozen importlib._bootstrap_external>\", line 940, in exec_module\n", "  File \"<frozen importlib._bootstrap>\", line 241, in _call_with_frames_removed\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/tesseract_ocr.py\", line 10, in <module>\n", "    import unstructured_pytesseract\n", "ModuleNotFoundError: No module named 'unstructured_pytesseract'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\", line 1536, in __call__\n", "    return self.wsgi_app(environ, start_response)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\", line 1514, in wsgi_app\n", "    response = self.handle_exception(e)\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\", line 1511, in wsgi_app\n", "    response = self.full_dispatch_request()\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\", line 919, in full_dispatch_request\n", "    rv = self.handle_user_exception(e)\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\", line 917, in full_dispatch_request\n", "    rv = self.dispatch_request()\n", "         ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/flask/app.py\", line 902, in dispatch_request\n", "    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Desktop/pythonenv/ragteszt/app.py\", line 37, in route_embed\n", "    embedded = embed(file)\n", "               ^^^^^^^^^^^\n", "  File \"/Users/<USER>/Desktop/pythonenv/ragteszt/embed.py\", line 45, in embed\n", "    chunks = load_and_split_data(file_path)\n", "             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Desktop/pythonenv/ragteszt/embed.py\", line 34, in load_and_split_data\n", "    data = loader.load()\n", "           ^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_core/document_loaders/base.py\", line 31, in load\n", "    return list(self.lazy_load())\n", "           ^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_community/document_loaders/unstructured.py\", line 107, in lazy_load\n", "    elements = self._get_elements()\n", "               ^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/langchain_community/document_loaders/pdf.py\", line 94, in _get_elements\n", "    return partition_pdf(filename=self.file_path, **self.unstructured_kwargs)  # type: ignore[arg-type]\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/documents/elements.py\", line 581, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/file_utils/filetype.py\", line 788, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/file_utils/filetype.py\", line 746, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/chunking/dispatch.py\", line 74, in wrapper\n", "    elements = func(*args, **kwargs)\n", "               ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py\", line 211, in partition_pdf\n", "    return partition_pdf_or_image(\n", "           ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py\", line 307, in partition_pdf_or_image\n", "    elements = _partition_pdf_or_image_local(\n", "               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py\", line 216, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf.py\", line 628, in _partition_pdf_or_image_local\n", "    final_document_layout = process_file_with_ocr(\n", "                            ^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py\", line 216, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py\", line 186, in process_file_with_ocr\n", "    raise e\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py\", line 173, in process_file_with_ocr\n", "    merged_page_layout = supplement_page_layout_with_ocr(\n", "                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/utils.py\", line 216, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/pdf_image/ocr.py\", line 209, in supplement_page_layout_with_ocr\n", "    ocr_agent = OCRAgent.get_agent(language=ocr_languages)\n", "                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py\", line 34, in get_agent\n", "    return cls.get_instance(ocr_agent_cls_qname, language)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/unstructured/partition/utils/ocr_models/ocr_interface.py\", line 52, in get_instance\n", "    raise RuntimeError(\n", "RuntimeError: Could not get the OCRAgent instance. Please check the OCR package and the OCR_AGENT environment variable.\n", "\n", "\n", "-->\n"]}], "source": ["!curl --request POST \\\n", "--url http://localhost:8080/embed \\\n", "--header 'Content-Type: multipart/form-data' \\\n", "--form file=@/Users/<USER>/Dropbox/Sales_LunchHu.pdf"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain-community[chromadb]\n", "  Using cached langchain_community-0.3.18-py3-none-any.whl.metadata (2.4 kB)\n", "\u001b[33mWARNING: langchain-community 0.3.18 does not provide the extra 'chromadb'\u001b[0m\u001b[33m\n", "\u001b[0mRequirement already satisfied: langchain-core<1.0.0,>=0.3.37 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-community[chromadb]) (0.3.39)\n", "Collecting langchain<1.0.0,>=0.3.19 (from langchain-community[chromadb])\n", "  Using cached langchain-0.3.19-py3-none-any.whl.metadata (7.9 kB)\n", "Collecting SQLAlchemy<3,>=1.4 (from langchain-community[chromadb])\n", "  Using cached SQLAlchemy-2.0.38-cp311-cp311-macosx_10_9_x86_64.whl.metadata (9.6 kB)\n", "Requirement already satisfied: requests<3,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-community[chromadb]) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-community[chromadb]) (6.0.2)\n", "Collecting aiohttp<4.0.0,>=3.8.3 (from langchain-community[chromadb])\n", "  Using cached aiohttp-3.11.13-cp311-cp311-macosx_10_9_x86_64.whl.metadata (7.7 kB)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-community[chromadb]) (9.0.0)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community[chromadb])\n", "  Using cached dataclasses_json-0.6.7-py3-none-any.whl.metadata (25 kB)\n", "Collecting pydantic-settings<3.0.0,>=2.4.0 (from langchain-community[chromadb])\n", "  Using cached pydantic_settings-2.8.0-py3-none-any.whl.metadata (3.5 kB)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-community[chromadb]) (0.3.10)\n", "Collecting httpx-sse<1.0.0,>=0.4.0 (from langchain-community[chromadb])\n", "  Using cached httpx_sse-0.4.0-py3-none-any.whl.metadata (9.0 kB)\n", "Collecting numpy<2,>=1.26.4 (from langchain-community[chromadb])\n", "  Using cached numpy-1.26.4-cp311-cp311-macosx_10_9_x86_64.whl.metadata (61 kB)\n", "Collecting aiohappyeyeballs>=2.3.0 (from aiohttp<4.0.0,>=3.8.3->langchain-community[chromadb])\n", "  Using cached aiohappyeyeballs-2.4.6-py3-none-any.whl.metadata (5.9 kB)\n", "Collecting aiosignal>=1.1.2 (from aiohttp<4.0.0,>=3.8.3->langchain-community[chromadb])\n", "  Using cached aiosignal-1.3.2-py2.py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: attrs>=17.3.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community[chromadb]) (24.3.0)\n", "Collecting frozenlist>=1.1.1 (from aiohttp<4.0.0,>=3.8.3->langchain-community[chromadb])\n", "  Using cached frozenlist-1.5.0-cp311-cp311-macosx_10_9_x86_64.whl.metadata (13 kB)\n", "Collecting multidict<7.0,>=4.5 (from aiohttp<4.0.0,>=3.8.3->langchain-community[chromadb])\n", "  Using cached multidict-6.1.0-cp311-cp311-macosx_10_9_x86_64.whl.metadata (5.0 kB)\n", "Collecting propcache>=0.2.0 (from aiohttp<4.0.0,>=3.8.3->langchain-community[chromadb])\n", "  Using cached propcache-0.3.0-cp311-cp311-macosx_10_9_x86_64.whl.metadata (10 kB)\n", "Collecting yarl<2.0,>=1.17.0 (from aiohttp<4.0.0,>=3.8.3->langchain-community[chromadb])\n", "  Using cached yarl-1.18.3-cp311-cp311-macosx_10_9_x86_64.whl.metadata (69 kB)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community[chromadb])\n", "  Using cached marshmallow-3.26.1-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community[chromadb])\n", "  Using cached typing_inspect-0.9.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting langchain-text-splitters<1.0.0,>=0.3.6 (from langchain<1.0.0,>=0.3.19->langchain-community[chromadb])\n", "  Using cached langchain_text_splitters-0.3.6-py3-none-any.whl.metadata (1.9 kB)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain<1.0.0,>=0.3.19->langchain-community[chromadb]) (2.10.6)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.37->langchain-community[chromadb]) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.37->langchain-community[chromadb]) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.37->langchain-community[chromadb]) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (0.27.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (0.23.0)\n", "Collecting python-dotenv>=0.21.0 (from pydantic-settings<3.0.0,>=2.4.0->langchain-community[chromadb])\n", "  Using cached python_dotenv-1.0.1-py3-none-any.whl.metadata (23 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langchain-community[chromadb]) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langchain-community[chromadb]) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langchain-community[chromadb]) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langchain-community[chromadb]) (2025.1.31)\n", "Collecting greenlet!=0.4.17 (from SQLAlchemy<3,>=1.4->langchain-community[chromadb])\n", "  Using cached greenlet-3.1.1-cp311-cp311-macosx_11_0_universal2.whl.metadata (3.8 kB)\n", "Requirement already satisfied: anyio in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (4.6.2)\n", "Requirement already satisfied: httpcore==1.* in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (1.0.2)\n", "Requirement already satisfied: sniffio in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (1.3.0)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community[chromadb]) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.37->langchain-community[chromadb]) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.19->langchain-community[chromadb]) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.19->langchain-community[chromadb]) (2.27.2)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community[chromadb])\n", "  Using cached mypy_extensions-1.0.0-py3-none-any.whl.metadata (1.1 kB)\n", "Using cached aiohttp-3.11.13-cp311-cp311-macosx_10_9_x86_64.whl (468 kB)\n", "Using cached dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Using cached httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)\n", "Using cached langchain-0.3.19-py3-none-any.whl (1.0 MB)\n", "Using cached numpy-1.26.4-cp311-cp311-macosx_10_9_x86_64.whl (20.6 MB)\n", "Using cached pydantic_settings-2.8.0-py3-none-any.whl (30 kB)\n", "Using cached SQLAlchemy-2.0.38-cp311-cp311-macosx_10_9_x86_64.whl (2.1 MB)\n", "Using cached langchain_community-0.3.18-py3-none-any.whl (2.5 MB)\n", "Using cached aiohappyeyeballs-2.4.6-py3-none-any.whl (14 kB)\n", "Using cached aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)\n", "Using cached frozenlist-1.5.0-cp311-cp311-macosx_10_9_x86_64.whl (54 kB)\n", "Using cached greenlet-3.1.1-cp311-cp311-macosx_11_0_universal2.whl (272 kB)\n", "Using cached langchain_text_splitters-0.3.6-py3-none-any.whl (31 kB)\n", "Using cached marshmallow-3.26.1-py3-none-any.whl (50 kB)\n", "Using cached multidict-6.1.0-cp311-cp311-macosx_10_9_x86_64.whl (29 kB)\n", "Using cached propcache-0.3.0-cp311-cp311-macosx_10_9_x86_64.whl (45 kB)\n", "Using cached python_dotenv-1.0.1-py3-none-any.whl (19 kB)\n", "Using cached typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Using cached yarl-1.18.3-cp311-cp311-macosx_10_9_x86_64.whl (94 kB)\n", "Using cached mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Installing collected packages: python-dotenv, propcache, numpy, mypy-extensions, multidict, marshmallow, httpx-sse, greenlet, frozenlist, aiohappyeyeballs, yarl, typing-inspect, SQLAlchemy, aiosignal, pydantic-settings, dataclasses-json, aiohttp, langchain-text-splitters, langchain, langchain-community\n", "  Attempting uninstall: numpy\n", "    Found existing installation: numpy 2.2.3\n", "    Uninstalling numpy-2.2.3:\n", "      Successfully uninstalled numpy-2.2.3\n", "Successfully installed SQLAlchemy-2.0.38 aiohappyeyeballs-2.4.6 aiohttp-3.11.13 aiosignal-1.3.2 dataclasses-json-0.6.7 frozenlist-1.5.0 greenlet-3.1.1 httpx-sse-0.4.0 langchain-0.3.19 langchain-community-0.3.18 langchain-text-splitters-0.3.6 marshmallow-3.26.1 multidict-6.1.0 mypy-extensions-1.0.0 numpy-1.26.4 propcache-0.3.0 pydantic-settings-2.8.0 python-dotenv-1.0.1 typing-inspect-0.9.0 yarl-1.18.3\n", "Collecting pdfminer.six\n", "  Using cached pdfminer.six-20240706-py3-none-any.whl.metadata (4.1 kB)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pdfminer.six) (3.3.2)\n", "Collecting cryptography>=36.0.0 (from pdfminer.six)\n", "  Using cached cryptography-44.0.1-cp39-abi3-macosx_10_9_universal2.whl.metadata (5.7 kB)\n", "Requirement already satisfied: cffi>=1.12 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from cryptography>=36.0.0->pdfminer.six) (1.17.1)\n", "Requirement already satisfied: pycparser in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six) (2.21)\n", "Using cached pdfminer.six-20240706-py3-none-any.whl (5.6 MB)\n", "Using cached cryptography-44.0.1-cp39-abi3-macosx_10_9_universal2.whl (6.6 MB)\n", "Installing collected packages: cryptography, pdfminer.six\n", "Successfully installed cryptography-44.0.1 pdfminer.six-20240706\n", "Channels:\n", " - conda-forge\n", " - defaults\n", "Platform: osx-64\n", "Collecting package metadata (repodata.json): done\n", "Solving environment: done\n", "\n", "## Package Plan ##\n", "\n", "  environment location: /opt/anaconda3/envs/rag\n", "\n", "  added / updated specs:\n", "    - l<PERSON><PERSON><PERSON>\n", "\n", "\n", "The following packages will be downloaded:\n", "\n", "    package                    |            build\n", "    ---------------------------|-----------------\n", "    certifi-2025.1.31          |     pyhd8ed1ab_0         159 KB  conda-forge\n", "    ------------------------------------------------------------\n", "                                           Total:         159 KB\n", "\n", "The following NEW packages will be INSTALLED:\n", "\n", "  aom                conda-forge/osx-64::aom-3.9.1-hf036a51_0 \n", "  dav1d              conda-forge/osx-64::dav1d-1.2.1-h0dc2134_0 \n", "  libavif16          conda-forge/osx-64::libavif16-1.1.1-h71406da_2 \n", "  libde265           conda-forge/osx-64::libde265-1.0.15-h7728843_0 \n", "  libheif            conda-forge/osx-64::libheif-1.19.5-gpl_hc62a4a2_100 \n", "  rav1e              conda-forge/osx-64::rav1e-0.6.6-h7205ca4_2 \n", "  svt-av1            conda-forge/osx-64::svt-av1-2.3.0-h97d8b74_0 \n", "  x265               conda-forge/osx-64::x265-3.5-hbb4e6a2_3 \n", "\n", "The following packages will be UPDATED:\n", "\n", "  ca-certificates    pkgs/main::ca-certificates-2024.12.31~ --> conda-forge::ca-certificates-2025.1.31-h8857fd0_0 \n", "  libcxx                pkgs/main::libcxx-14.0.6-h9765a3e_0 --> conda-forge::libcxx-19.1.7-hf95d169_0 \n", "  openssl              pkgs/main::openssl-3.0.15-h46256e1_0 --> conda-forge::openssl-3.4.1-hc426f3f_0 \n", "\n", "The following packages will be SUPERSEDED by a higher-priority channel:\n", "\n", "  certifi            pkgs/main/osx-64::certifi-2025.1.31-p~ --> conda-forge/noarch::certifi-2025.1.31-pyhd8ed1ab_0 \n", "\n", "\n", "Proceed ([y]/n)? ^C\n", "Collecting pi_heif\n", "  Downloading pi_heif-0.21.0.tar.gz (16.2 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m16.2/16.2 MB\u001b[0m \u001b[31m2.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:02\u001b[0m\n", "\u001b[?25h  Installing build dependencies ... \u001b[?25l\\"]}], "source": ["\n", "!pip install -U langchain-community[chromadb]\n", "\n", "!pip install pdfminer.six\n", "!conda install -c conda-forge libheif\n", "!pip install --no-cache-dir pi_heif\n", "!pip install unstructured-inference\n", "\n", "!conda install -c conda-forge poppler"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pdf2image\n", "  Using cached pdf2image-1.17.0-py3-none-any.whl.metadata (6.2 kB)\n", "Requirement already satisfied: pillow in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pdf2image) (11.1.0)\n", "Using cached pdf2image-1.17.0-py3-none-any.whl (11 kB)\n", "Installing collected packages: pdf2image\n", "Successfully installed pdf2image-1.17.0\n", "Collecting pytesseract\n", "  Using cached pytesseract-0.3.13-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: packaging>=21.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pytesseract) (24.2)\n", "Requirement already satisfied: Pillow>=8.0.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pytesseract) (11.1.0)\n", "Using cached pytesseract-0.3.13-py3-none-any.whl (14 kB)\n", "Installing collected packages: pytesseract\n", "Successfully installed pytesseract-0.3.13\n", "Collecting unstructured[pytesseract]\n", "  Using cached unstructured-0.16.23-py3-none-any.whl.metadata (24 kB)\n", "\u001b[33mWARNING: unstructured 0.16.23 does not provide the extra 'pytesseract'\u001b[0m\u001b[33m\n", "\u001b[0mCollecting chardet (from unstructured[pytesseract])\n", "  Using cached chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)\n", "Collecting filetype (from unstructured[pytesseract])\n", "  Using cached filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)\n", "Collecting python-magic (from unstructured[pytesseract])\n", "  Using cached python_magic-0.4.27-py2.py3-none-any.whl.metadata (5.8 kB)\n", "Requirement already satisfied: lxml in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (5.3.1)\n", "Collecting nltk (from unstructured[pytesseract])\n", "  Using cached nltk-3.9.1-py3-none-any.whl.metadata (2.9 kB)\n", "Requirement already satisfied: requests in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (2.32.3)\n", "Requirement already satisfied: beautifulsoup4 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (4.12.3)\n", "Collecting emoji (from unstructured[pytesseract])\n", "  Using cached emoji-2.14.1-py3-none-any.whl.metadata (5.7 kB)\n", "Requirement already satisfied: dataclasses-json in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (0.6.7)\n", "Collecting python-iso639 (from unstructured[pytesseract])\n", "  Using cached python_iso639-2025.2.18-py3-none-any.whl.metadata (14 kB)\n", "Collecting langdetect (from unstructured[pytesseract])\n", "  Using cached langdetect-1.0.9-py3-none-any.whl\n", "Requirement already satisfied: numpy<2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (1.26.4)\n", "Requirement already satisfied: rapidfuzz in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (3.12.1)\n", "Collecting backoff (from unstructured[pytesseract])\n", "  Using cached backoff-2.2.1-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: typing-extensions in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (4.12.2)\n", "Collecting unstructured-client (from unstructured[pytesseract])\n", "  Downloading unstructured_client-0.30.4-py3-none-any.whl.metadata (22 kB)\n", "Collecting wrapt (from unstructured[pytesseract])\n", "  Using cached wrapt-1.17.2-cp311-cp311-macosx_10_9_x86_64.whl.metadata (6.4 kB)\n", "Requirement already satisfied: tqdm in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (4.67.1)\n", "Requirement already satisfied: psutil in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured[pytesseract]) (5.9.0)\n", "Collecting python-oxmsg (from unstructured[pytesseract])\n", "  Using cached python_oxmsg-0.0.2-py3-none-any.whl.metadata (5.0 kB)\n", "Collecting html5lib (from unstructured[pytesseract])\n", "  Using cached html5lib-1.1-py2.py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: soupsieve>1.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from beautifulsoup4->unstructured[pytesseract]) (2.5)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from dataclasses-json->unstructured[pytesseract]) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from dataclasses-json->unstructured[pytesseract]) (0.9.0)\n", "Requirement already satisfied: six>=1.9 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from html5lib->unstructured[pytesseract]) (1.16.0)\n", "Requirement already satisfied: webencodings in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from html5lib->unstructured[pytesseract]) (0.5.1)\n", "Collecting click (from nltk->unstructured[pytesseract])\n", "  Using cached click-8.1.8-py3-none-any.whl.metadata (2.3 kB)\n", "Requirement already satisfied: joblib in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from nltk->unstructured[pytesseract]) (1.4.2)\n", "Requirement already satisfied: regex>=2021.8.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from nltk->unstructured[pytesseract]) (2024.11.6)\n", "Collecting olefile (from python-oxmsg->unstructured[pytesseract])\n", "  Using cached olefile-0.47-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->unstructured[pytesseract]) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->unstructured[pytesseract]) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->unstructured[pytesseract]) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->unstructured[pytesseract]) (2025.1.31)\n", "Collecting aiofiles>=24.1.0 (from unstructured-client->unstructured[pytesseract])\n", "  Using cached aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: cryptography>=3.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured-client->unstructured[pytesseract]) (44.0.1)\n", "Collecting eval-type-backport>=0.2.0 (from unstructured-client->unstructured[pytesseract])\n", "  Using cached eval_type_backport-0.2.2-py3-none-any.whl.metadata (2.2 kB)\n", "Requirement already satisfied: httpx>=0.27.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured-client->unstructured[pytesseract]) (0.27.0)\n", "Requirement already satisfied: nest-asyncio>=1.6.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured-client->unstructured[pytesseract]) (1.6.0)\n", "Requirement already satisfied: pydantic>=2.10.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured-client->unstructured[pytesseract]) (2.10.6)\n", "Collecting pypdf>=4.0 (from unstructured-client->unstructured[pytesseract])\n", "  Using cached pypdf-5.3.0-py3-none-any.whl.metadata (7.2 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured-client->unstructured[pytesseract]) (2.9.0.post0)\n", "Requirement already satisfied: requests-toolbelt>=1.0.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from unstructured-client->unstructured[pytesseract]) (1.0.0)\n", "Requirement already satisfied: cffi>=1.12 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from cryptography>=3.1->unstructured-client->unstructured[pytesseract]) (1.17.1)\n", "Requirement already satisfied: anyio in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx>=0.27.0->unstructured-client->unstructured[pytesseract]) (4.6.2)\n", "Requirement already satisfied: httpcore==1.* in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx>=0.27.0->unstructured-client->unstructured[pytesseract]) (1.0.2)\n", "Requirement already satisfied: sniffio in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx>=0.27.0->unstructured-client->unstructured[pytesseract]) (1.3.0)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpcore==1.*->httpx>=0.27.0->unstructured-client->unstructured[pytesseract]) (0.14.0)\n", "Requirement already satisfied: packaging>=17.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->unstructured[pytesseract]) (24.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pydantic>=2.10.3->unstructured-client->unstructured[pytesseract]) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pydantic>=2.10.3->unstructured-client->unstructured[pytesseract]) (2.27.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json->unstructured[pytesseract]) (1.0.0)\n", "Requirement already satisfied: pycparser in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from cffi>=1.12->cryptography>=3.1->unstructured-client->unstructured[pytesseract]) (2.21)\n", "Using cached backoff-2.2.1-py3-none-any.whl (15 kB)\n", "Using cached chardet-5.2.0-py3-none-any.whl (199 kB)\n", "Using cached emoji-2.14.1-py3-none-any.whl (590 kB)\n", "Using cached filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Using cached html5lib-1.1-py2.py3-none-any.whl (112 kB)\n", "Using cached nltk-3.9.1-py3-none-any.whl (1.5 MB)\n", "Using cached python_iso639-2025.2.18-py3-none-any.whl (167 kB)\n", "Using cached python_magic-0.4.27-py2.py3-none-any.whl (13 kB)\n", "Using cached python_oxmsg-0.0.2-py3-none-any.whl (31 kB)\n", "Using cached unstructured-0.16.23-py3-none-any.whl (1.8 MB)\n", "Downloading unstructured_client-0.30.4-py3-none-any.whl (164 kB)\n", "Using cached wrapt-1.17.2-cp311-cp311-macosx_10_9_x86_64.whl (38 kB)\n", "Using cached aiofiles-24.1.0-py3-none-any.whl (15 kB)\n", "Using cached eval_type_backport-0.2.2-py3-none-any.whl (5.8 kB)\n", "Using cached pypdf-5.3.0-py3-none-any.whl (300 kB)\n", "Using cached click-8.1.8-py3-none-any.whl (98 kB)\n", "Using cached olefile-0.47-py2.py3-none-any.whl (114 kB)\n", "Installing collected packages: filetype, wrapt, python-magic, python-iso639, pypdf, olefile, langdetect, html5lib, eval-type-backport, emoji, click, chardet, backoff, aiofiles, python-oxmsg, nltk, unstructured-client, unstructured\n", "Successfully installed aiofiles-24.1.0 backoff-2.2.1 chardet-5.2.0 click-8.1.8 emoji-2.14.1 eval-type-backport-0.2.2 filetype-1.2.0 html5lib-1.1 langdetect-1.0.9 nltk-3.9.1 olefile-0.47 pypdf-5.3.0 python-iso639-2025.2.18 python-magic-0.4.27 python-oxmsg-0.0.2 unstructured-0.16.23 unstructured-client-0.30.4 wrapt-1.17.2\n", "Requirement already satisfied: pillow in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (11.1.0)\n", "Requirement already satisfied: opencv-python in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (*********)\n", "Requirement already satisfied: numpy>=1.21.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from opencv-python) (1.26.4)\n"]}], "source": ["!pip install pdf2image\n", "!pip install pytesseract\n", "!export OCR_AGENT=/usr/bin/tesseract\n", "!pip install unstructured[pytesseract]\n", "\n", "!pip install pillow opencv-python"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "TesseractNotFoundError", "evalue": "/usr/bin/tesseract is not installed or it's not in your PATH. See README file for more information.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m/opt/anaconda3/envs/rag/lib/python3.11/site-packages/pytesseract/pytesseract.py:451\u001b[0m, in \u001b[0;36mget_tesseract_version\u001b[0;34m()\u001b[0m\n\u001b[1;32m    450\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 451\u001b[0m     output \u001b[38;5;241m=\u001b[39m subprocess\u001b[38;5;241m.\u001b[39mcheck_output(\n\u001b[1;32m    452\u001b[0m         [tesseract_cmd, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m--version\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    453\u001b[0m         stderr\u001b[38;5;241m=\u001b[39msubprocess\u001b[38;5;241m.\u001b[39mSTDOUT,\n\u001b[1;32m    454\u001b[0m         env\u001b[38;5;241m=\u001b[39menviron,\n\u001b[1;32m    455\u001b[0m         stdin\u001b[38;5;241m=\u001b[39msubprocess\u001b[38;5;241m.\u001b[39mDEVNULL,\n\u001b[1;32m    456\u001b[0m     )\n\u001b[1;32m    457\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m:\n", "File \u001b[0;32m/opt/anaconda3/envs/rag/lib/python3.11/subprocess.py:466\u001b[0m, in \u001b[0;36mcheck_output\u001b[0;34m(timeout, *popenargs, **kwargs)\u001b[0m\n\u001b[1;32m    464\u001b[0m     kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124minput\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m empty\n\u001b[0;32m--> 466\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m run(\u001b[38;5;241m*\u001b[39mpopenargs, stdout\u001b[38;5;241m=\u001b[39mPIPE, timeout\u001b[38;5;241m=\u001b[39mtimeout, check\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m    467\u001b[0m            \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\u001b[38;5;241m.\u001b[39mstdout\n", "File \u001b[0;32m/opt/anaconda3/envs/rag/lib/python3.11/subprocess.py:548\u001b[0m, in \u001b[0;36mrun\u001b[0;34m(input, capture_output, timeout, check, *popenargs, **kwargs)\u001b[0m\n\u001b[1;32m    546\u001b[0m     kwargs[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstderr\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m PIPE\n\u001b[0;32m--> 548\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m <PERSON>n(\u001b[38;5;241m*\u001b[39mpopenargs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs) \u001b[38;5;28;01mas\u001b[39;00m process:\n\u001b[1;32m    549\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n", "File \u001b[0;32m/opt/anaconda3/envs/rag/lib/python3.11/subprocess.py:1026\u001b[0m, in \u001b[0;36mPopen.__init__\u001b[0;34m(self, args, bufsize, executable, stdin, stdout, stderr, preexec_fn, close_fds, shell, cwd, env, universal_newlines, startupinfo, creationflags, restore_signals, start_new_session, pass_fds, user, group, extra_groups, encoding, errors, text, umask, pipesize, process_group)\u001b[0m\n\u001b[1;32m   1023\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr \u001b[38;5;241m=\u001b[39m io\u001b[38;5;241m.\u001b[39mTextIOWrapper(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr,\n\u001b[1;32m   1024\u001b[0m                     encoding\u001b[38;5;241m=\u001b[39mencoding, errors\u001b[38;5;241m=\u001b[39merrors)\n\u001b[0;32m-> 1026\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_execute_child(args, executable, preexec_fn, close_fds,\n\u001b[1;32m   1027\u001b[0m                         pass_fds, cwd, env,\n\u001b[1;32m   1028\u001b[0m                         startupinfo, creationflags, shell,\n\u001b[1;32m   1029\u001b[0m                         p2cread, p2cwrite,\n\u001b[1;32m   1030\u001b[0m                         c2pread, c2pwrite,\n\u001b[1;32m   1031\u001b[0m                         errread, errwrite,\n\u001b[1;32m   1032\u001b[0m                         restore_signals,\n\u001b[1;32m   1033\u001b[0m                         gid, gids, uid, umask,\n\u001b[1;32m   1034\u001b[0m                         start_new_session, process_group)\n\u001b[1;32m   1035\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[1;32m   1036\u001b[0m     \u001b[38;5;66;03m# Cleanup if the child failed starting.\u001b[39;00m\n", "File \u001b[0;32m/opt/anaconda3/envs/rag/lib/python3.11/subprocess.py:1955\u001b[0m, in \u001b[0;36mPopen._execute_child\u001b[0;34m(self, args, executable, preexec_fn, close_fds, pass_fds, cwd, env, startupinfo, creationflags, shell, p2cread, p2cwrite, c2pread, c2pwrite, errread, errwrite, restore_signals, gid, gids, uid, umask, start_new_session, process_group)\u001b[0m\n\u001b[1;32m   1954\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m err_filename \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 1955\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m child_exception_type(errno_num, err_msg, err_filename)\n\u001b[1;32m   1956\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '/usr/bin/tesseract'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mTesseractNotFoundError\u001b[0m                    <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[3], line 6\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;66;03m# Set the correct path for Tess<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m      5\u001b[0m pytesseract\u001b[38;5;241m.\u001b[39mpytesseract\u001b[38;5;241m.\u001b[39mtesseract_cmd \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/usr/bin/tesseract\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m----> 6\u001b[0m \u001b[38;5;28mprint\u001b[39m(pytesseract\u001b[38;5;241m.\u001b[39mget_tesseract_version())\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# Tesztkép (cser<PERSON><PERSON> le egy valódi képfájlra)\u001b[39;00m\n\u001b[1;32m      9\u001b[0m image \u001b[38;5;241m=\u001b[39m Image\u001b[38;5;241m.\u001b[39mopen(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/Users/<USER>/Downloads/qr-code.png\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m/opt/anaconda3/envs/rag/lib/python3.11/site-packages/pytesseract/pytesseract.py:163\u001b[0m, in \u001b[0;36mrun_once.<locals>.wrapper\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    160\u001b[0m \u001b[38;5;129m@wraps\u001b[39m(func)\n\u001b[1;32m    161\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mwrapper\u001b[39m(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[1;32m    162\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcached\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;28;01m<PERSON><PERSON>e\u001b[39;00m) \u001b[38;5;129;01mor\u001b[39;00m wrapper\u001b[38;5;241m.\u001b[39m_result \u001b[38;5;129;01mis\u001b[39;00m wrapper:\n\u001b[0;32m--> 163\u001b[0m         wrapper\u001b[38;5;241m.\u001b[39m_result \u001b[38;5;241m=\u001b[39m func(\u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    164\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m wrapper\u001b[38;5;241m.\u001b[39m_result\n", "File \u001b[0;32m/opt/anaconda3/envs/rag/lib/python3.11/site-packages/pytesseract/pytesseract.py:458\u001b[0m, in \u001b[0;36mget_tesseract_version\u001b[0;34m()\u001b[0m\n\u001b[1;32m    451\u001b[0m     output \u001b[38;5;241m=\u001b[39m subprocess\u001b[38;5;241m.\u001b[39mcheck_output(\n\u001b[1;32m    452\u001b[0m         [tesseract_cmd, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m--version\u001b[39m\u001b[38;5;124m'\u001b[39m],\n\u001b[1;32m    453\u001b[0m         stderr\u001b[38;5;241m=\u001b[39msubprocess\u001b[38;5;241m.\u001b[39mSTDOUT,\n\u001b[1;32m    454\u001b[0m         env\u001b[38;5;241m=\u001b[39menviron,\n\u001b[1;32m    455\u001b[0m         stdin\u001b[38;5;241m=\u001b[39msubprocess\u001b[38;5;241m.\u001b[39mDEVNULL,\n\u001b[1;32m    456\u001b[0m     )\n\u001b[1;32m    457\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mOSError\u001b[39;00m:\n\u001b[0;32m--> 458\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m TesseractNotFoundError()\n\u001b[1;32m    460\u001b[0m raw_version \u001b[38;5;241m=\u001b[39m output\u001b[38;5;241m.\u001b[39mdecode(DEFAULT_ENCODING)\n\u001b[1;32m    461\u001b[0m str_version, \u001b[38;5;241m*\u001b[39m_ \u001b[38;5;241m=\u001b[39m raw_version\u001b[38;5;241m.\u001b[39mlstrip(string\u001b[38;5;241m.\u001b[39mprintable[\u001b[38;5;241m10\u001b[39m:])\u001b[38;5;241m.\u001b[39mpartition(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mTesseractNotFoundError\u001b[0m: /usr/bin/tesseract is not installed or it's not in your PATH. See README file for more information."]}], "source": ["import pytesseract\n", "from PIL import Image\n", "\n", "# Set the correct path for Tess<PERSON><PERSON>\n", "pytesseract.pytesseract.tesseract_cmd = \"/usr/bin/tesseract\"\n", "print(pytesseract.get_tesseract_version())\n", "\n", "# Tesztkép (c<PERSON><PERSON><PERSON> le egy valódi képfájlra)\n", "image = Image.open(\"/Users/<USER>/Downloads/qr-code.png\")\n", "\n", "# OCR végrehajtása\n", "text = pytesseract.image_to_string(image)\n", "print(text)\n"]}], "metadata": {"kernelspec": {"display_name": "rag", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}