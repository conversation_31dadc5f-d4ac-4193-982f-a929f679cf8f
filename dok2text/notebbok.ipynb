{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mFailed to start the Kernel. \n", "\u001b[1;31mTraceback (most recent call last):\n", "\u001b[1;31m  File \"<frozen runpy>\", line 198, in _run_module_as_main\n", "\u001b[1;31m  File \"<frozen runpy>\", line 88, in _run_code\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/ipykernel_launcher.py\", line 16, in <module>\n", "\u001b[1;31m    from ipykernel import kernelapp as app\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 21, in <module>\n", "\u001b[1;31m    from IPython.core.application import (  # type:ignore[attr-defined]\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/IPython/__init__.py\", line 55, in <module>\n", "\u001b[1;31m    from .terminal.embed import embed\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/IPython/terminal/embed.py\", line 15, in <module>\n", "\u001b[1;31m    from IPython.core.interactiveshell import DummyMod, InteractiveShell\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 110, in <module>\n", "\u001b[1;31m    from IPython.core.history import HistoryManager\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/site-packages/IPython/core/history.py\", line 10, in <module>\n", "\u001b[1;31m    import sqlite3\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/sqlite3/__init__.py\", line 57, in <module>\n", "\u001b[1;31m    from sqlite3.dbapi2 import *\n", "\u001b[1;31m  File \"/opt/anaconda3/envs/rag/lib/python3.11/sqlite3/dbapi2.py\", line 27, in <module>\n", "\u001b[1;31m    from _sqlite3 import *\n", "\u001b[1;31mImportError: dlopen(/opt/anaconda3/envs/rag/lib/python3.11/lib-dynload/_sqlite3.cpython-311-darwin.so, 0x0002): Symbol not found: (_sqlite3_deserialize)\n", "\u001b[1;31m  Referenced from: '/opt/anaconda3/envs/rag/lib/python3.11/lib-dynload/_sqlite3.cpython-311-darwin.so'\n", "\u001b[1;31m  Expected in: '/opt/anaconda3/envs/rag/lib/libsqlite3.0.dylib'. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["#dokumentum konverter program"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Python 3.11.11\n", "Collecting PyPDF2\n", "  Using cached pypdf2-3.0.1-py3-none-any.whl.metadata (6.8 kB)\n", "Using cached pypdf2-3.0.1-py3-none-any.whl (232 kB)\n", "Installing collected packages: PyPDF2\n", "Successfully installed PyPDF2-3.0.1\n"]}], "source": ["!python3 --version\n", "!pip install PyPDF2"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting openpyxl\n", "  Using cached openpyxl-3.1.5-py2.py3-none-any.whl.metadata (2.5 kB)\n", "Collecting et-xmlfile (from openpyxl)\n", "  Using cached et_xmlfile-2.0.0-py3-none-any.whl.metadata (2.7 kB)\n", "Using cached openpyxl-3.1.5-py2.py3-none-any.whl (250 kB)\n", "Using cached et_xmlfile-2.0.0-py3-none-any.whl (18 kB)\n", "Installing collected packages: et-xmlfile, openpyxl\n", "Successfully installed et-xmlfile-2.0.0 openpyxl-3.1.5\n"]}], "source": ["!pip install openpyxl"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pandas\n", "  Using cached pandas-2.2.3-cp311-cp311-macosx_10_9_x86_64.whl.metadata (89 kB)\n", "Collecting numpy>=1.23.2 (from pandas)\n", "  Using cached numpy-2.2.3-cp311-cp311-macosx_10_9_x86_64.whl.metadata (62 kB)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from pandas) (2.9.0.post0)\n", "Collecting pytz>=2020.1 (from pandas)\n", "  Using cached pytz-2025.1-py2.py3-none-any.whl.metadata (22 kB)\n", "Collecting tzdata>=2022.7 (from pandas)\n", "  Using cached tzdata-2025.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Requirement already satisfied: six>=1.5 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from python-dateutil>=2.8.2->pandas) (1.16.0)\n", "Using cached pandas-2.2.3-cp311-cp311-macosx_10_9_x86_64.whl (12.6 MB)\n", "Using cached numpy-2.2.3-cp311-cp311-macosx_10_9_x86_64.whl (21.2 MB)\n", "Using cached pytz-2025.1-py2.py3-none-any.whl (507 kB)\n", "Using cached tzdata-2025.1-py2.py3-none-any.whl (346 kB)\n", "Installing collected packages: pytz, tzdata, numpy, pandas\n", "Successfully installed numpy-2.2.3 pandas-2.2.3 pytz-2025.1 tzdata-2025.1\n"]}], "source": ["!pip install pandas"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting python-docx\n", "  Using cached python_docx-1.1.2-py3-none-any.whl.metadata (2.0 kB)\n", "Collecting lxml>=3.1.0 (from python-docx)\n", "  Using cached lxml-5.3.1-cp311-cp311-macosx_10_9_x86_64.whl.metadata (3.7 kB)\n", "Requirement already satisfied: typing-extensions>=4.9.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from python-docx) (4.12.2)\n", "Using cached python_docx-1.1.2-py3-none-any.whl (244 kB)\n", "Using cached lxml-5.3.1-cp311-cp311-macosx_10_9_x86_64.whl (4.4 MB)\n", "Installing collected packages: lxml, python-docx\n", "Successfully installed lxml-5.3.1 python-docx-1.1.2\n"]}], "source": ["!pip install python-docx"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting sentence-transformers\n", "  Using cached sentence_transformers-3.4.1-py3-none-any.whl.metadata (10 kB)\n", "Collecting transformers<5.0.0,>=4.41.0 (from sentence-transformers)\n", "  Using cached transformers-4.49.0-py3-none-any.whl.metadata (44 kB)\n", "Collecting tqdm (from sentence-transformers)\n", "  Using cached tqdm-4.67.1-py3-none-any.whl.metadata (57 kB)\n", "Collecting torch>=1.11.0 (from sentence-transformers)\n", "  Using cached torch-2.2.2-cp311-none-macosx_10_9_x86_64.whl.metadata (25 kB)\n", "Collecting scikit-learn (from sentence-transformers)\n", "  Using cached scikit_learn-1.6.1-cp311-cp311-macosx_10_9_x86_64.whl.metadata (31 kB)\n", "Collecting scipy (from sentence-transformers)\n", "  Using cached scipy-1.15.2-cp311-cp311-macosx_10_13_x86_64.whl.metadata (61 kB)\n", "Collecting huggingface-hub>=0.20.0 (from sentence-transformers)\n", "  Using cached huggingface_hub-0.29.1-py3-none-any.whl.metadata (13 kB)\n", "Collecting <PERSON><PERSON> (from sentence-transformers)\n", "  Using cached pillow-11.1.0-cp311-cp311-macosx_10_10_x86_64.whl.metadata (9.1 kB)\n", "Collecting filelock (from huggingface-hub>=0.20.0->sentence-transformers)\n", "  Using cached filelock-3.17.0-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting fsspec>=2023.5.0 (from huggingface-hub>=0.20.0->sentence-transformers)\n", "  Using cached fsspec-2025.2.0-py3-none-any.whl.metadata (11 kB)\n", "Requirement already satisfied: packaging>=20.9 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (6.0.2)\n", "Requirement already satisfied: requests in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (2.32.3)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from huggingface-hub>=0.20.0->sentence-transformers) (4.12.2)\n", "Collecting sympy (from torch>=1.11.0->sentence-transformers)\n", "  Using cached sympy-1.13.3-py3-none-any.whl.metadata (12 kB)\n", "Collecting networkx (from torch>=1.11.0->sentence-transformers)\n", "  Using cached networkx-3.4.2-py3-none-any.whl.metadata (6.3 kB)\n", "Requirement already satisfied: jinja2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from torch>=1.11.0->sentence-transformers) (3.1.5)\n", "Requirement already satisfied: numpy>=1.17 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from transformers<5.0.0,>=4.41.0->sentence-transformers) (2.2.3)\n", "Collecting regex!=2019.12.17 (from transformers<5.0.0,>=4.41.0->sentence-transformers)\n", "  Using cached regex-2024.11.6-cp311-cp311-macosx_10_9_x86_64.whl.metadata (40 kB)\n", "Collecting tokenizers<0.22,>=0.21 (from transformers<5.0.0,>=4.41.0->sentence-transformers)\n", "  Using cached tokenizers-0.21.0-cp39-abi3-macosx_10_12_x86_64.whl.metadata (6.7 kB)\n", "Collecting safetensors>=0.4.1 (from transformers<5.0.0,>=4.41.0->sentence-transformers)\n", "  Using cached safetensors-0.5.2-cp38-abi3-macosx_10_12_x86_64.whl.metadata (3.8 kB)\n", "Collecting joblib>=1.2.0 (from scikit-learn->sentence-transformers)\n", "  Using cached joblib-1.4.2-py3-none-any.whl.metadata (5.4 kB)\n", "Collecting threadpoolctl>=3.1.0 (from scikit-learn->sentence-transformers)\n", "  Using cached threadpoolctl-3.5.0-py3-none-any.whl.metadata (13 kB)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from jinja2->torch>=1.11.0->sentence-transformers) (3.0.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.3.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests->huggingface-hub>=0.20.0->sentence-transformers) (2025.1.31)\n", "Collecting mpmath<1.4,>=1.1.0 (from sympy->torch>=1.11.0->sentence-transformers)\n", "  Using cached mpmath-1.3.0-py3-none-any.whl.metadata (8.6 kB)\n", "Using cached sentence_transformers-3.4.1-py3-none-any.whl (275 kB)\n", "Using cached huggingface_hub-0.29.1-py3-none-any.whl (468 kB)\n", "Using cached torch-2.2.2-cp311-none-macosx_10_9_x86_64.whl (150.8 MB)\n", "Using cached tqdm-4.67.1-py3-none-any.whl (78 kB)\n", "Using cached transformers-4.49.0-py3-none-any.whl (10.0 MB)\n", "Using cached pillow-11.1.0-cp311-cp311-macosx_10_10_x86_64.whl (3.2 MB)\n", "Using cached scikit_learn-1.6.1-cp311-cp311-macosx_10_9_x86_64.whl (12.1 MB)\n", "Using cached scipy-1.15.2-cp311-cp311-macosx_10_13_x86_64.whl (38.7 MB)\n", "Using cached fsspec-2025.2.0-py3-none-any.whl (184 kB)\n", "Using cached joblib-1.4.2-py3-none-any.whl (301 kB)\n", "Using cached regex-2024.11.6-cp311-cp311-macosx_10_9_x86_64.whl (287 kB)\n", "Using cached safetensors-0.5.2-cp38-abi3-macosx_10_12_x86_64.whl (427 kB)\n", "Using cached threadpoolctl-3.5.0-py3-none-any.whl (18 kB)\n", "Using cached tokenizers-0.21.0-cp39-abi3-macosx_10_12_x86_64.whl (2.6 MB)\n", "Using cached filelock-3.17.0-py3-none-any.whl (16 kB)\n", "Using cached networkx-3.4.2-py3-none-any.whl (1.7 MB)\n", "Using cached sympy-1.13.3-py3-none-any.whl (6.2 MB)\n", "Using cached mpmath-1.3.0-py3-none-any.whl (536 kB)\n", "Installing collected packages: mpmath, tqdm, threadpoolctl, sympy, scipy, safetensors, regex, Pillow, networkx, joblib, fsspec, filelock, torch, scikit-learn, huggingface-hub, tokenizers, transformers, sentence-transformers\n", "Successfully installed Pillow-11.1.0 filelock-3.17.0 fsspec-2025.2.0 huggingface-hub-0.29.1 joblib-1.4.2 mpmath-1.3.0 networkx-3.4.2 regex-2024.11.6 safetensors-0.5.2 scikit-learn-1.6.1 scipy-1.15.2 sentence-transformers-3.4.1 sympy-1.13.3 threadpoolctl-3.5.0 tokenizers-0.21.0 torch-2.2.2 tqdm-4.67.1 transformers-4.49.0\n"]}], "source": ["!pip install sentence-transformers"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting faiss-cpu\n", "  Using cached faiss_cpu-1.10.0-cp311-cp311-macosx_10_14_x86_64.whl.metadata (4.4 kB)\n", "Requirement already satisfied: numpy<3.0,>=1.25.0 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from faiss-cpu) (2.2.3)\n", "Requirement already satisfied: packaging in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from faiss-cpu) (24.2)\n", "Using cached faiss_cpu-1.10.0-cp311-cp311-macosx_10_14_x86_64.whl (7.7 MB)\n", "Installing collected packages: faiss-cpu\n", "Successfully installed faiss-cpu-1.10.0\n"]}], "source": ["!pip install faiss-cpu"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting PyCryptodome\n", "  Using cached pycryptodome-3.21.0-cp36-abi3-macosx_10_9_x86_64.whl.metadata (3.4 kB)\n", "Using cached pycryptodome-3.21.0-cp36-abi3-macosx_10_9_x86_64.whl (1.6 MB)\n", "Installing collected packages: PyCryptodome\n", "Successfully installed PyCryptodome-3.21.0\n"]}], "source": ["!pip install PyCryptodome"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain-ollama\n", "  Using cached langchain_ollama-0.2.3-py3-none-any.whl.metadata (1.9 kB)\n", "Collecting langchain-core<0.4.0,>=0.3.33 (from langchain-ollama)\n", "  Using cached langchain_core-0.3.39-py3-none-any.whl.metadata (5.9 kB)\n", "Collecting ollama<1,>=0.4.4 (from langchain-ollama)\n", "  Using cached ollama-0.4.7-py3-none-any.whl.metadata (4.7 kB)\n", "Collecting langsmith<0.4,>=0.1.125 (from langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached langsmith-0.3.10-py3-none-any.whl.metadata (14 kB)\n", "Collecting tenacity!=8.4.0,<10.0.0,>=8.1.0 (from langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached tenacity-9.0.0-py3-none-any.whl.metadata (1.2 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached jsonpatch-1.33-py2.py3-none-any.whl.metadata (3.0 kB)\n", "Requirement already satisfied: PyYAML>=5.3 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<0.4.0,>=0.3.33->langchain-ollama) (6.0.2)\n", "Requirement already satisfied: packaging<25,>=23.2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<0.4.0,>=0.3.33->langchain-ollama) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langchain-core<0.4.0,>=0.3.33->langchain-ollama) (4.12.2)\n", "Collecting pydantic<3.0.0,>=2.5.2 (from langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached pydantic-2.10.6-py3-none-any.whl.metadata (30 kB)\n", "Requirement already satisfied: httpx<0.29,>=0.27 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from ollama<1,>=0.4.4->langchain-ollama) (0.27.0)\n", "Requirement already satisfied: anyio in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<0.29,>=0.27->ollama<1,>=0.4.4->langchain-ollama) (4.6.2)\n", "Requirement already satisfied: certifi in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<0.29,>=0.27->ollama<1,>=0.4.4->langchain-ollama) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<0.29,>=0.27->ollama<1,>=0.4.4->langchain-ollama) (1.0.2)\n", "Requirement already satisfied: idna in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<0.29,>=0.27->ollama<1,>=0.4.4->langchain-ollama) (3.7)\n", "Requirement already satisfied: sniffio in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpx<0.29,>=0.27->ollama<1,>=0.4.4->langchain-ollama) (1.3.0)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from httpcore==1.*->httpx<0.29,>=0.27->ollama<1,>=0.4.4->langchain-ollama) (0.14.0)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached jsonpointer-3.0.0-py2.py3-none-any.whl.metadata (2.3 kB)\n", "Collecting or<PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached orjson-3.10.15-cp311-cp311-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl.metadata (41 kB)\n", "Requirement already satisfied: requests<3,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.33->langchain-ollama) (2.32.3)\n", "Collecting requests-toolbelt<2.0.0,>=1.0.0 (from langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached requests_toolbelt-1.0.0-py2.py3-none-any.whl.metadata (14 kB)\n", "Collecting zstandard<0.24.0,>=0.23.0 (from langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached zstandard-0.23.0-cp311-cp311-macosx_10_9_x86_64.whl.metadata (3.0 kB)\n", "Collecting annotated-types>=0.6.0 (from pydantic<3.0.0,>=2.5.2->langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached annotated_types-0.7.0-py3-none-any.whl.metadata (15 kB)\n", "Collecting pydantic-core==2.27.2 (from pydantic<3.0.0,>=2.5.2->langchain-core<0.4.0,>=0.3.33->langchain-ollama)\n", "  Using cached pydantic_core-2.27.2-cp311-cp311-macosx_10_12_x86_64.whl.metadata (6.6 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.33->langchain-ollama) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/anaconda3/envs/rag/lib/python3.11/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<0.4.0,>=0.3.33->langchain-ollama) (2.3.0)\n", "Using cached langchain_ollama-0.2.3-py3-none-any.whl (19 kB)\n", "Using cached langchain_core-0.3.39-py3-none-any.whl (414 kB)\n", "Using cached ollama-0.4.7-py3-none-any.whl (13 kB)\n", "Using cached jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Using cached langsmith-0.3.10-py3-none-any.whl (333 kB)\n", "Using cached pydantic-2.10.6-py3-none-any.whl (431 kB)\n", "Using cached pydantic_core-2.27.2-cp311-cp311-macosx_10_12_x86_64.whl (1.9 MB)\n", "Using cached tenacity-9.0.0-py3-none-any.whl (28 kB)\n", "Using cached annotated_types-0.7.0-py3-none-any.whl (13 kB)\n", "Using cached jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Using cached orjson-3.10.15-cp311-cp311-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl (249 kB)\n", "Using cached requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)\n", "Using cached zstandard-0.23.0-cp311-cp311-macosx_10_9_x86_64.whl (788 kB)\n", "Installing collected packages: zstandard, tenacity, pydantic-core, orjson, jsonpointer, annotated-types, requests-toolbelt, pydantic, jsonpatch, ollama, langsmith, langchain-core, langchain-ollama\n", "Successfully installed annotated-types-0.7.0 jsonpatch-1.33 jsonpointer-3.0.0 langchain-core-0.3.39 langchain-ollama-0.2.3 langsmith-0.3.10 ollama-0.4.7 orjson-3.10.15 pydantic-2.10.6 pydantic-core-2.27.2 requests-toolbelt-1.0.0 tenacity-9.0.0 zstandard-0.23.0\n"]}], "source": ["!pip install langchain-ollama"]}], "metadata": {"kernelspec": {"display_name": "rag", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}