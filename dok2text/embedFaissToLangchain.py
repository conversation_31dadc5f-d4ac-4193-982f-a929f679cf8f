import faiss
import os
from langchain_community.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings

TEXT_EMBEDDING_MODEL = os.getenv('TEXT_EMBEDDING_MODEL', 'nomic-embed-text')

index = faiss.read_index("index_faiss.bin")
embeddings = OllamaEmbeddings(model=TEXT_EMBEDDING_MODEL)
vectordb = FAISS(embedding_function=embeddings, index=index, docstore={}, index_to_docstore_id={})

vectordb.save_local("faiss_db")
#vectordb.save_local("faiss_db")