import faiss
import numpy as np
import ollama
import os
from langchain_ollama import OllamaEmbeddings

# Step 1: Load the FAISS index
index = faiss.read_index("index_faiss.bin")

TEXT_EMBEDDING_MODEL = os.getenv('TEXT_EMBEDDING_MODEL', 'nomic-embed-text')
embeddings = OllamaEmbeddings(model=TEXT_EMBEDDING_MODEL)

query = "Mennyi az adó?"
query_vector = embeddings.embed_query(query)

# Step 2: Prepare your query vector
# Example: Let's assume you're working with a 128-dimensional vector. Replace this with your actual query data.
query_vector = np.array(query_vector, dtype=np.float32)

# Step 3: Perform the search in the FAISS index
k = 5  # Number of nearest neighbors to search for
distances, indices = index.search(query_vector, k)

# Step 4: Process the search results
# You can print the results (for example, the indices and their corresponding distances)
print("Indices of top 5 nearest neighbors:", indices)
print("Distances of top 5 nearest neighbors:", distances)

# Step 5: Optionally, integrate with Ollama for additional processing
# If the vectors represent text or other data, you can use <PERSON>lla<PERSON>'s model for further processing.
# Example: Assuming you want to use the indices to get some text and process it with Ollama.
# Replace 'your_model_name' with the actual model you're using.

# Example: Suppose the indices correspond to documents or text; you would fetch them and pass to Ollama.
# In this case, you might want to query a database or retrieve the text data associated with the indices.
# This is just a placeholder example.
texts_to_process = ["Text associated with index {}".format(i) for i in indices[0]]

# Now invoke the Ollama model on the retrieved text data
for text in texts_to_process:
    result = ollama.invoke("llama3.2", input_text=text)
    print(f"Ollama model result for '{text}': {result}")
