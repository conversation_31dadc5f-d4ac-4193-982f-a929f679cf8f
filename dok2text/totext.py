import PyPDF2
import json
import pandas as pd
from docx import Document
import os
from sentence_transformers import SentenceTransformer
import faiss
import numpy as np
import mylib as mylib

#os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"


folder_path = "dok2text/dok"
documents = mylib.load_documents(folder_path)
print(f"Betöltött dokumentumok száma: {len(documents)}")  

documents = [mylib.clean_text(doc) for doc in documents]
#print (documents)

# Sentence Transformer
model = SentenceTransformer("all-MiniLM-L6-v2")
vectors = model.encode(documents)
print (vectors.shape)

# Faiss
dimension = vectors.shape[1]
index = faiss.IndexFlatL2(dimension)
index.add(vectors)
print(f"Az indexben tárolt vektorok: {index.ntotal}")

faiss.write_index(index, "index_faiss.bin")
index = faiss.read_index("index_faiss.bin")

#new_documents = documents
new_documents = mylib.add_to_index(index, model, documents)
new_vectors = model.encode(new_documents)
if len(new_documents) == 0:
    print("A lista üres")
else:
    #index.add(new_vectors)
    for vector in new_vectors:
        if not index.contains(vector):
            index.add([vector])

print(f"Frissített index mérete: {index.ntotal}")

# Keresési lekérdezés
query = "Kicsoda Wendy?"
query_vector = model.encode([query])

# K legközelebbi szomszéd keresése (például K=2)
k = 2
distances, indices = index.search(query_vector, k)

print (range(k))

# Találatok megjelenítése
for i in range(min(k, len(documents))):
    print (i)
    print(f"Találat {i+1}: {documents[indices[0][i]]} (távolság: {distances[0][i]})")
