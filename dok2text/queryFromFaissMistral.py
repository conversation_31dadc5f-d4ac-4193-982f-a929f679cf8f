import faiss
import os
import numpy as np
from langchain_community.vectorstores import FAISS
from langchain_ollama import OllamaEmbeddings
from langchain_community.chat_models import ChatOllama
from langchain.schema import SystemMessage, HumanMessage

import mylib as mylib

# 1️⃣ Embedding modell létrehozása (Mistral-al)
TEXT_EMBEDDING_MODEL = os.getenv('TEXT_EMBEDDING_MODEL', 'nomic-embed-text')
embeddings = OllamaEmbeddings(model=TEXT_EMBEDDING_MODEL)

# 2️⃣ FAISS index betöltése
try:
    index = faiss.read_index("index_faiss.bin")
except Exception as e:
    print(f"Error loading FAISS index: {e}")
    exit(1)


vectordb = FAISS(embedding_function=embeddings, index=index, docstore={}, index_to_docstore_id={})
print(f"Index dimension: {vectordb.index.d}")
for key, value in vars(vectordb).items():
    print(f"{key}: {value}")

# 3️⃣ Kérdés beágyazása és keresés a FAISS-ben
query = "Mi a legjobb módja egy FAISS index keresésének?"
query_vector = embeddings.embed_query(query)

# Convert query_vector to a 2D NumPy array
query_vector = np.array(query_vector).reshape(1, -1)
#query_vector = np.expand_dims(query_vector, axis=0)

# Ensure the query vector dimension matches the index dimension
if query_vector.shape[1] != vectordb.index.d:
    print(f"Dimension mismatch: query vector dimension {query_vector.shape[1]} does not match index dimension {vectordb.index.d}")
    exit(1)

# Perform similarity search by vector
#docs_and_scores = vectordb.similarity_search_with_score_by_vector(query_vector, k=3)  # Top 3 legközelebbi találat
#docs = [doc for doc, score in docs_and_scores]

# Perform similarity search by vector
scores, indices = index.search(query_vector, k=3)  # Top 3 legközelebbi találat

#print(indices[0])  # Nézd meg az indexek listáját
#print(vectordb)  # Nézd meg az index_to_docstore_id szótárat

#docs = [vectordb.docstore[vectordb.index_to_docstore_id[idx]] for idx in indices[0]]


# 4️⃣ Kontextus összeállítása a chatbot számára
#retrieved_context = "\n\n".join([doc.page_content for doc in docs])
#chat_context = f"A következő információkat találtam a FAISS adatbázisban:\n\n{retrieved_context}\n\nMost válaszolj a kérdésre ezen adatok alapján."

# 5️⃣ ChatOllama meghívása
#chatollama = ChatOllama(model="mistral")

#messages = [
#    SystemMessage(content="Te egy intelligens asszisztens vagy, aki a következő adatok alapján válaszol."),
#    HumanMessage(content=chat_context)
#]

#response = chatollama(messages)
#print(response.content)  # Kiírja a választ 
