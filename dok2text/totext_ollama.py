import PyPDF2
import json
import pandas as pd
from docx import Document
import os
import faiss
import numpy as np
from langchain_ollama import OllamaEmbeddings

os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

def extract_text_from_pdf(file_path):
    with open(file_path, 'rb') as file:
        pdf = PyPDF2.PdfReader(file)
        text = ''
        for page in pdf.pages:
            text += page.extract_text()
        return text

def extract_text_from_docx(file_path):
    doc = Document(file_path)
    text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
    return text

def load_documents(folder_path):
    documents = []
    for filename in os.listdir(folder_path):
        file_path = os.path.join(folder_path, filename)
        if filename.endswith(".txt"):
            with open(file_path, "r", encoding="utf-8") as file:
                documents.append(file.read())
        elif filename.endswith(".pdf"):
            documents.append(extract_text_from_pdf(file_path))
        elif filename.endswith(".docx"):
            documents.append(extract_text_from_docx(file_path))
        elif filename.endswith(".csv"):
            df = pd.read_csv(file_path)
            documents.extend(df["content"].tolist())
        elif filename.endswith(".json"):
            with open(file_path, "r", encoding="utf-8") as file:
                data = json.load(file)
                documents.extend([item["content"] for item in data])
    return documents

def clean_text(text):
    text = text.strip()
    text = text.replace("\n", " ")
    return text

def add_to_index(index, model, documents, threshold=0.99):
    new_vectors = model.embed_documents(documents)
    unique_vectors = []
    unique_documents = []
    
    for doc, vec in zip(documents, new_vectors):
        D, I = index.search(np.array([vec]), 1)  # 1 legközelebbi találat keresése
        if D[0][0] > threshold:  # Ha nincs nagyon hasonló dokumentum az indexben
            unique_vectors.append(vec)
            unique_documents.append(doc)

    if unique_vectors:
        index.add(np.array(unique_vectors))
    
    return unique_documents

folder_path = "dok2text/dok"
documents = load_documents(folder_path)
print(f"Betöltött dokumentumok száma: {len(documents)}")  

documents = [clean_text(doc) for doc in documents]
print(documents)

# Ollama Embeddings
TEXT_EMBEDDING_MODEL = os.getenv('TEXT_EMBEDDING_MODEL', 'nomic-embed-text')
model = OllamaEmbeddings(model=TEXT_EMBEDDING_MODEL)
vectors = model.embed_documents(documents)
vectors = np.array(vectors)
print(vectors.shape)

# Faiss
dimension = vectors.shape[1]
index = faiss.IndexFlatL2(dimension)
index.add(vectors)
print(f"Az indexben tárolt vektorok: {index.ntotal}")

faiss.write_index(index, "index_faiss.bin")
index = faiss.read_index("index_faiss.bin")

#new_documents = documents
new_documents = add_to_index(index, model, documents)
new_vectors = model.embed_documents(new_documents)
new_vectors = np.array(new_vectors)
if len(new_documents) == 0:
    print("A lista üres")
else:
    index.add(new_vectors)

print(f"Frissített index mérete: {index.ntotal}")
